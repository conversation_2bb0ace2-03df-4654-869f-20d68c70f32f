from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from datetime import datetime
import aiosqlite
import json
from typing import Dict, List, Optional

from logger import logger, bot_logger
from database_manager import db_manager
from error_handler import error_handler
from utils.auth import AuthUtils

class UserSearchService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.sql_db_path = db_manager.sql_db_path
        logger.info("UserSearchService initialized successfully")

    async def show_user_search_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user search menu"""
        try:
            user_id = update.effective_user.id
            
            # Only admins can access this
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            message = """🔍 **جست‌وجوی کاربر**

برای جست‌وجو، آیدی عددی تلگرام کاربر را ارسال کنید.

📝 **مثال:** `123456789`

⚠️ **توجه:** کاربر باید قبلاً از ربات استفاده کرده باشد."""

            # Set user state for receiving user ID
            context.user_data['admin_action'] = 'search_user'
            
            await update.message.reply_text(message, parse_mode='Markdown')
            
            bot_logger.log_admin_action(user_id, "USER_SEARCH_MENU", "Accessed user search menu")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_search_menu")

    async def process_user_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE, search_user_id: str) -> None:
        """Process user search by ID"""
        try:
            admin_id = update.effective_user.id
            
            # Validate user ID
            try:
                target_user_id = int(search_user_id.strip())
            except ValueError:
                await update.message.reply_text("❌ آیدی عددی نامعتبر است. لطفاً یک عدد صحیح ارسال کنید.")
                return

            # Get user information
            user_info = await self.get_user_complete_info(target_user_id)
            
            if not user_info:
                await update.message.reply_text("❌ کاربر با این آیدی در ربات یافت نشد.")
                return

            # Display user information
            await self.display_user_info(update, context, user_info)
            
            # Clear admin action
            context.user_data.pop('admin_action', None)
            
            bot_logger.log_admin_action(admin_id, "USER_SEARCH", f"Searched for user {target_user_id}")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_user_search")

    async def get_user_complete_info(self, user_id: int) -> Optional[Dict]:
        """Get complete user information"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Get basic user info
                cursor = await db.execute("""
                    SELECT user_id, username, first_name, last_name, wallet_balance, 
                           total_purchases, total_spent, is_banned, created_at, updated_at
                    FROM users WHERE user_id = ?
                """, (user_id,))
                user_data = await cursor.fetchone()
                
                if not user_data:
                    return None
                
                # Get purchase statistics
                cursor = await db.execute("""
                    SELECT COUNT(*), SUM(price) FROM single_orders 
                    WHERE user_id = ? AND status = 'completed'
                """, (user_id,))
                single_orders = await cursor.fetchone()
                
                cursor = await db.execute("""
                    SELECT COUNT(*), SUM(price) FROM plan_orders 
                    WHERE user_id = ? AND status = 'completed'
                """, (user_id,))
                plan_orders = await cursor.fetchone()
                
                # Get wallet transactions
                cursor = await db.execute("""
                    SELECT COUNT(*), SUM(amount) FROM wallet_transactions 
                    WHERE user_id = ? AND transaction_type = 'deposit' AND status = 'completed'
                """, (user_id,))
                deposits = await cursor.fetchone()
                
                # Get Apple IDs count
                from models.user_purchases import user_purchases_model
                apple_ids = await user_purchases_model.get_user_apple_ids(user_id)
                
                return {
                    'user_id': user_data[0],
                    'username': user_data[1],
                    'first_name': user_data[2],
                    'last_name': user_data[3],
                    'wallet_balance': user_data[4] or 0,
                    'total_purchases': user_data[5] or 0,
                    'total_spent': user_data[6] or 0,
                    'is_banned': user_data[7] or 0,
                    'created_at': user_data[8],
                    'updated_at': user_data[9],
                    'single_orders_count': single_orders[0] or 0,
                    'single_orders_amount': single_orders[1] or 0,
                    'plan_orders_count': plan_orders[0] or 0,
                    'plan_orders_amount': plan_orders[1] or 0,
                    'deposits_count': deposits[0] or 0,
                    'deposits_amount': deposits[1] or 0,
                    'apple_ids_count': len(apple_ids)
                }
                
        except Exception as e:
            logger.error(f"Error getting user complete info: {str(e)}")
            return None

    async def display_user_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_info: Dict) -> None:
        """Display complete user information with management options"""
        try:
            username_display = f"@{user_info['username']}" if user_info['username'] else "بدون نام کاربری"
            name_display = f"{user_info['first_name'] or ''} {user_info['last_name'] or ''}".strip() or "بدون نام"
            
            ban_status = "🔴 بن شده" if user_info['is_banned'] else "🟢 فعال"
            
            message = f"""👤 **اطلاعات کامل کاربر**

🆔 **آیدی:** `{user_info['user_id']}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
🔘 **وضعیت:** {ban_status}

💰 **اطلاعات مالی:**
• موجودی کیف پول: {user_info['wallet_balance']:,} تومان
• کل واریزها: {user_info['deposits_amount']:,} تومان ({user_info['deposits_count']} تراکنش)
• کل خرج شده: {user_info['total_spent']:,} تومان

🛒 **آمار خرید:**
• خریدهای تکی: {user_info['single_orders_count']} سفارش ({user_info['single_orders_amount']:,} تومان)
• خریدهای پلن: {user_info['plan_orders_count']} سفارش ({user_info['plan_orders_amount']:,} تومان)
• کل Apple ID ها: {user_info['apple_ids_count']} عدد

📅 **تاریخ‌ها:**
• عضویت: {user_info['created_at'][:10] if user_info['created_at'] else 'نامشخص'}
• آخرین فعالیت: {user_info['updated_at'][:10] if user_info['updated_at'] else 'نامشخص'}

🔧 **عملیات مدیریتی:**"""

            keyboard = [
                [InlineKeyboardButton("🗑️ پاکسازی خریدها", callback_data=f"clear_purchases_{user_info['user_id']}")],
                [InlineKeyboardButton("📱 پاکسازی Apple ID ها", callback_data=f"clear_apple_ids_{user_info['user_id']}")],
                [InlineKeyboardButton("💰 تغییر موجودی کیف پول", callback_data=f"change_wallet_{user_info['user_id']}")],
            ]
            
            if user_info['is_banned']:
                keyboard.append([InlineKeyboardButton("✅ رفع بن کاربر", callback_data=f"unban_user_{user_info['user_id']}")])
            else:
                keyboard.append([InlineKeyboardButton("🚫 بن کاربر", callback_data=f"ban_user_{user_info['user_id']}")])
            
            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="admin_panel")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "display_user_info")

    async def clear_user_purchases(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int) -> None:
        """Clear all user purchases"""
        try:
            admin_id = update.effective_user.id

            async with aiosqlite.connect(self.sql_db_path) as db:
                # Clear single orders
                await db.execute("DELETE FROM single_orders WHERE user_id = ?", (user_id,))

                # Clear plan orders
                await db.execute("DELETE FROM plan_orders WHERE user_id = ?", (user_id,))

                # Clear legacy purchases
                await db.execute("DELETE FROM purchases WHERE user_id = ?", (user_id,))

                # Reset user purchase stats
                await db.execute("""
                    UPDATE users SET total_purchases = 0, total_spent = 0, updated_at = ?
                    WHERE user_id = ?
                """, (datetime.now().isoformat(), user_id))

                await db.commit()

            await update.callback_query.answer("✅ تمام خریدهای کاربر پاک شد", show_alert=True)
            await update.callback_query.edit_message_text(
                f"✅ **عملیات موفق**\n\nتمام خریدهای کاربر `{user_id}` پاک شد.",
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(admin_id, "CLEAR_USER_PURCHASES", f"Cleared purchases for user {user_id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "clear_user_purchases")

    async def clear_user_apple_ids(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int) -> None:
        """Clear all user Apple IDs by marking them as available again"""
        try:
            admin_id = update.effective_user.id

            # Get user's Apple IDs
            from models.user_purchases import user_purchases_model
            apple_ids = await user_purchases_model.get_user_apple_ids(user_id)

            if not apple_ids:
                await update.callback_query.answer("❌ کاربر هیچ Apple ID ندارد", show_alert=True)
                return

            # Mark Apple IDs as available in Excel
            count = 0
            for apple_id_info in apple_ids:
                email = apple_id_info.get('email')
                if email:
                    success = db_manager.mark_apple_id_as_available(email)
                    if success:
                        count += 1

            # Clear from database
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Clear single orders Apple IDs
                await db.execute("UPDATE single_orders SET apple_id_assigned = NULL WHERE user_id = ?", (user_id,))

                # Clear plan orders Apple IDs
                await db.execute("UPDATE plan_orders SET apple_ids_assigned = NULL WHERE user_id = ?", (user_id,))

                # Clear legacy purchases
                await db.execute("DELETE FROM purchases WHERE user_id = ?", (user_id,))

                await db.commit()

            await update.callback_query.answer(f"✅ {count} Apple ID آزاد شد", show_alert=True)
            await update.callback_query.edit_message_text(
                f"✅ **عملیات موفق**\n\n{count} Apple ID کاربر `{user_id}` آزاد شد و قابل فروش مجدد است.",
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(admin_id, "CLEAR_USER_APPLE_IDS", f"Cleared {count} Apple IDs for user {user_id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "clear_user_apple_ids")

    async def show_change_wallet_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int) -> None:
        """Show wallet change menu"""
        try:
            # Get current balance
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute("SELECT wallet_balance, first_name FROM users WHERE user_id = ?", (user_id,))
                user_data = await cursor.fetchone()

                if not user_data:
                    await update.callback_query.answer("❌ کاربر یافت نشد", show_alert=True)
                    return

            current_balance = user_data[0] or 0
            user_name = user_data[1] or "نامشخص"

            message = f"""💰 **تغییر موجودی کیف پول**

👤 **کاربر:** {user_name} (`{user_id}`)
💰 **موجودی فعلی:** {current_balance:,} تومان

📝 **مبلغ جدید را ارسال کنید:**
• برای صفر کردن: `0`
• برای مبلغ مثبت: `100000`
• برای مبلغ منفی: `-50000`

⚠️ **توجه:** این عمل موجودی کیف پول را مستقیماً تغییر می‌دهد."""

            # Set admin state
            context.user_data['admin_action'] = 'change_wallet'
            context.user_data['target_user_id'] = user_id

            await update.callback_query.edit_message_text(message, parse_mode='Markdown')

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_change_wallet_menu")

    async def process_wallet_change(self, update: Update, context: ContextTypes.DEFAULT_TYPE, amount_text: str) -> None:
        """Process wallet balance change"""
        try:
            admin_id = update.effective_user.id
            target_user_id = context.user_data.get('target_user_id')

            if not target_user_id:
                await update.message.reply_text("❌ خطا در شناسایی کاربر")
                return

            # Validate amount
            try:
                new_amount = int(amount_text.strip())
            except ValueError:
                await update.message.reply_text("❌ مبلغ نامعتبر است. لطفاً یک عدد صحیح ارسال کنید.")
                return

            # Update wallet balance
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute("SELECT wallet_balance, first_name FROM users WHERE user_id = ?", (target_user_id,))
                user_data = await cursor.fetchone()

                if not user_data:
                    await update.message.reply_text("❌ کاربر یافت نشد")
                    return

                old_balance = user_data[0] or 0
                user_name = user_data[1] or "نامشخص"

                # Update balance
                await db.execute("""
                    UPDATE users SET wallet_balance = ?, updated_at = ? WHERE user_id = ?
                """, (new_amount, datetime.now().isoformat(), target_user_id))

                # Create transaction record
                await db.execute("""
                    INSERT INTO wallet_transactions (user_id, transaction_type, amount, status, description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    target_user_id,
                    'admin_adjustment',
                    new_amount - old_balance,
                    'completed',
                    f'تغییر موجودی توسط ادمین - از {old_balance:,} به {new_amount:,}',
                    datetime.now().isoformat()
                ))

                await db.commit()

            message = f"""✅ **موجودی کیف پول تغییر کرد**

👤 **کاربر:** {user_name} (`{target_user_id}`)
💰 **موجودی قبلی:** {old_balance:,} تومان
💰 **موجودی جدید:** {new_amount:,} تومان
📊 **تغییر:** {new_amount - old_balance:+,} تومان"""

            await update.message.reply_text(message, parse_mode='Markdown')

            # Clear admin state
            context.user_data.pop('admin_action', None)
            context.user_data.pop('target_user_id', None)

            bot_logger.log_admin_action(admin_id, "CHANGE_USER_WALLET", f"Changed wallet for user {target_user_id}: {old_balance} -> {new_amount}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_wallet_change")

    async def ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int) -> None:
        """Ban a user"""
        try:
            admin_id = update.effective_user.id

            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute("SELECT first_name, is_banned FROM users WHERE user_id = ?", (user_id,))
                user_data = await cursor.fetchone()

                if not user_data:
                    await update.callback_query.answer("❌ کاربر یافت نشد", show_alert=True)
                    return

                if user_data[1]:  # Already banned
                    await update.callback_query.answer("❌ کاربر قبلاً بن شده است", show_alert=True)
                    return

                # Ban user
                await db.execute("""
                    UPDATE users SET is_banned = 1, updated_at = ? WHERE user_id = ?
                """, (datetime.now().isoformat(), user_id))

                await db.commit()

            user_name = user_data[0] or "نامشخص"

            await update.callback_query.answer("✅ کاربر بن شد", show_alert=True)
            await update.callback_query.edit_message_text(
                f"🚫 **کاربر بن شد**\n\nکاربر {user_name} (`{user_id}`) از ربات بن شد.",
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(admin_id, "BAN_USER", f"Banned user {user_id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "ban_user")

    async def unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int) -> None:
        """Unban a user"""
        try:
            admin_id = update.effective_user.id

            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute("SELECT first_name, is_banned FROM users WHERE user_id = ?", (user_id,))
                user_data = await cursor.fetchone()

                if not user_data:
                    await update.callback_query.answer("❌ کاربر یافت نشد", show_alert=True)
                    return

                if not user_data[1]:  # Not banned
                    await update.callback_query.answer("❌ کاربر بن نیست", show_alert=True)
                    return

                # Unban user
                await db.execute("""
                    UPDATE users SET is_banned = 0, updated_at = ? WHERE user_id = ?
                """, (datetime.now().isoformat(), user_id))

                await db.commit()

            user_name = user_data[0] or "نامشخص"

            await update.callback_query.answer("✅ بن کاربر رفع شد", show_alert=True)
            await update.callback_query.edit_message_text(
                f"✅ **بن کاربر رفع شد**\n\nکاربر {user_name} (`{user_id}`) می‌تواند دوباره از ربات استفاده کند.",
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(admin_id, "UNBAN_USER", f"Unbanned user {user_id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "unban_user")

# Global instance
user_search_service = UserSearchService()
