from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import json
import aiosqlite

from logger import logger, bot_logger
from error_handler import error_handler
from models.purchase_plans import purchase_plans_model
from models.wallet import wallet_model
from database_manager import db_manager
from messages import *
from services.settings_service import settings_service

class PurchaseService:
    def __init__(self):
        logger.info("PurchaseService initialized successfully")
    
    async def show_purchase_plans(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show domain selection for purchase plans"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested purchase plans")

            # Show domain selection first
            await self.show_domain_selection_for_purchase(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_purchase_plans")

    async def show_domain_selection_for_purchase(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show domain selection for purchasing plans"""
        try:
            from database_manager import db_manager

            # Get available domains that have plans
            plans = await purchase_plans_model.get_all_plans(active_only=True)

            if not plans:
                # Get dynamic button text and icon
                plans_text = settings_service.get_button_text("purchase_plans")
                plans_icon = plans_text.split()[0] if plans_text else "📦"
                await update.message.reply_text(f"{plans_icon} {NO_PURCHASE_PLANS}")
                return

            # Group plans by domain
            domains_with_plans = {}
            for plan in plans:
                domain = plan.get('domain', 'نامشخص')
                if domain not in domains_with_plans:
                    domains_with_plans[domain] = []
                domains_with_plans[domain].append(plan)

            message = "🌐 **انتخاب دامنه برای خرید پلن**\n\n"
            message += "لطفاً دامنه مورد نظر را انتخاب کنید:\n\n"

            keyboard = []
            for domain, domain_plans in domains_with_plans.items():
                plan_count = len(domain_plans)
                button_text = f"🌐 {domain} ({plan_count} پلن)"
                keyboard.append([InlineKeyboardButton(
                    button_text,
                    callback_data=f"select_domain_for_purchase_{domain}"
                )])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Check if this is from callback query or message
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_domain_selection_for_purchase")

    async def show_plans_for_domain(self, update: Update, context: ContextTypes.DEFAULT_TYPE, domain: str) -> None:
        """Show purchase plans for specific domain"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested plans for domain {domain}")

            # Get user balance
            balance = await wallet_model.get_user_balance(user_id)

            # Get plans for specific domain
            all_plans = await purchase_plans_model.get_all_plans(active_only=True)
            plans = [plan for plan in all_plans if plan.get('domain', '') == domain]

            if not plans:
                await update.callback_query.edit_message_text(
                    f"❌ هیچ پلنی برای دامنه {domain} موجود نیست.",
                    parse_mode='Markdown'
                )
                return

            # Get dynamic button text and icon
            plans_text = settings_service.get_button_text("purchase_plans")
            plans_icon = plans_text.split()[0] if plans_text else "📦"

            message = f"{plans_icon} **پلن‌های خرید - {domain}**\n\n"
            message += f"💰 **موجودی کیف پول:** {balance:,} تومان\n\n"

            keyboard = []

            for plan in plans:
                warranty_text = ""
                if plan['has_warranty']:
                    warranty_text = f" (گارانتی {plan['warranty_days']} روزه)"

                plan_text = f"📱 {plan['name']}{warranty_text}\n"
                plan_text += f"💰 قیمت: {plan['price']:,} تومان\n"
                plan_text += f"📊 تعداد: {plan['quantity']} عدد\n"

                if plan['description']:
                    plan_text += f"📝 {plan['description']}\n"

                message += plan_text + "\n" + "─" * 30 + "\n\n"

                # Add buy button
                button_text = f"خرید {plan['quantity']} عدد ({plan['price']:,} تومان)"
                keyboard.append([InlineKeyboardButton(
                    button_text,
                    callback_data=f"buy_plan_{plan['id']}"
                )])

            # Add wallet and back buttons
            keyboard.append([InlineKeyboardButton(BTN_WALLET, callback_data="wallet_menu")])
            keyboard.append([InlineKeyboardButton("🔙 بازگشت به انتخاب دامنه", callback_data="back_to_domain_selection")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message, parse_mode='Markdown', reply_markup=reply_markup
            )

            bot_logger.log_user_action(
                user_id,
                update.effective_user.username or "N/A",
                "VIEW_DOMAIN_PLANS",
                f"Viewed {len(plans)} plans for domain {domain}"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_plans_for_domain")
    
    async def process_plan_purchase(self, update: Update, context: ContextTypes.DEFAULT_TYPE, plan_id: int) -> None:
        """Process purchase of a specific plan - send to admin for approval"""
        try:
            user_id = update.effective_user.id

            # Get plan details
            plan = await purchase_plans_model.get_plan_by_id(plan_id)
            if not plan or not plan['is_active']:
                await update.callback_query.answer(PLAN_NOT_AVAILABLE, show_alert=True)
                return

            # Check if user has pending plan orders
            pending_order = await self.get_pending_plan_order(user_id)
            if pending_order:
                message = f"""❌ **نمی‌توانید سفارش جدید ثبت کنید**

📋 **سفارش در انتظار:**
• پلن: {pending_order.get('plan_name', 'نامشخص')}
• تعداد: {pending_order.get('quantity', 0)} عدد
• قیمت: {pending_order.get('price', 0):,} تومان
• تاریخ: {pending_order.get('created_at', '')[:16]}

⏳ لطفاً منتظر تایید یا رد سفارش قبلی باشید."""

                await update.callback_query.edit_message_text(message, parse_mode='Markdown')
                return

            # Check user wallet balance
            from models.wallet import wallet_model
            user_balance = await wallet_model.get_user_balance(user_id)

            if user_balance < plan['price']:
                insufficient_message = f"""💰 **موجودی ناکافی**

💳 **موجودی فعلی:** {user_balance:,} تومان
💸 **مبلغ مورد نیاز:** {plan['price']:,} تومان
❌ **کمبود:** {plan['price'] - user_balance:,} تومان

💡 **برای شارژ کیف پول خود از دکمه زیر استفاده کنید:**"""

                keyboard = [[InlineKeyboardButton("💰 شارژ کیف پول", callback_data="wallet_deposit")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    insufficient_message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return

            # Check availability
            available_apple_ids = db_manager.get_available_apple_ids()
            domain_available = []
            for aid in available_apple_ids:
                email = aid.get('Email', '')  # Use 'Email' with capital E
                extracted_domain = db_manager.extract_domain_from_email(str(email))
                if extracted_domain == plan['domain']:
                    domain_available.append(aid)

            logger.debug(f"Plan purchase - Domain {plan['domain']} availability: {len(domain_available)} out of {plan['quantity']} required")

            if len(domain_available) < plan['quantity']:
                await update.callback_query.answer(
                    f"❌ متأسفانه تعداد کافی اکانت {plan['domain']} موجود نیست.\nموجود: {len(domain_available)} عدد",
                    show_alert=True
                )
                return

            # Create plan order ID
            import uuid
            order_id = str(uuid.uuid4())[:8]

            # Save plan order data
            order_data = {
                'order_id': order_id,
                'user_id': user_id,
                'username': update.effective_user.username or "N/A",
                'first_name': update.effective_user.first_name or "N/A",
                'last_name': update.effective_user.last_name or "N/A",
                'plan_id': plan_id,
                'plan_name': plan['name'],
                'domain': plan['domain'],
                'quantity': plan['quantity'],
                'price': plan['price'],
                'status': 'pending_approval',
                'created_at': datetime.now().isoformat()
            }

            await self.save_plan_order(order_data)

            logger.info(f"Plan order {order_id} created for user {user_id}: {plan['name']} - {plan['price']:,} تومان")

            # Send order to approval group immediately
            await self.send_plan_order_to_approval_group(context, order_data)

            message = f"""✅ **سفارش پلن ثبت شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• پلن: {plan['name']}
• دامنه: {plan['domain']}
• تعداد: {plan['quantity']} عدد
• قیمت: {plan['price']:,} تومان

⏳ **وضعیت:** در انتظار تایید ادمین

📤 **مرحله بعد:**
سفارش شما به ادمین‌ها ارسال شد و پس از تایید، اکانت‌ها برای شما ارسال خواهند شد.

⚠️ **نکته:** برای لغو سفارش /cancel را ارسال کنید."""

            await update.callback_query.edit_message_text(message, parse_mode='Markdown')

            bot_logger.log_user_action(
                user_id,
                update.effective_user.username or "N/A",
                "START_PLAN_PURCHASE",
                f"Order {order_id}: {plan['name']} - {plan['price']:,} تومان"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_plan_purchase")
    


    async def get_pending_plan_order(self, user_id: int) -> Optional[Dict]:
        """Get user's pending plan order"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Check if table exists first
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='plan_orders'"
                )
                table_exists = await cursor.fetchone()

                if not table_exists:
                    logger.debug("plan_orders table does not exist, returning None")
                    return None

                cursor = await db.execute(
                    "SELECT * FROM plan_orders WHERE user_id = ? AND status = 'pending_approval' ORDER BY rowid DESC LIMIT 1",
                    (user_id,)
                )
                row = await cursor.fetchone()

                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None

        except Exception as e:
            logger.error(f"Error getting pending plan order: {str(e)}")
            return None

    async def save_plan_order(self, order_data: Dict) -> bool:
        """Save plan order to database"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Check if table exists and has required columns
                cursor = await db.execute("PRAGMA table_info(plan_orders)")
                columns = await cursor.fetchall()
                column_names = [col[1] for col in columns]

                if not columns:  # Table doesn't exist
                    await db.execute("""
                        CREATE TABLE plan_orders (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            order_id TEXT UNIQUE NOT NULL,
                            user_id INTEGER NOT NULL,
                            username TEXT,
                            first_name TEXT,
                            last_name TEXT,
                            plan_id INTEGER NOT NULL,
                            plan_name TEXT NOT NULL,
                            domain TEXT NOT NULL,
                            quantity INTEGER NOT NULL,
                            price INTEGER NOT NULL,
                            status TEXT NOT NULL DEFAULT 'pending_approval',
                            created_at TEXT NOT NULL,
                            updated_at TEXT
                        )
                    """)
                elif 'username' not in column_names:  # Table exists but missing columns
                    # Add missing columns
                    await db.execute("ALTER TABLE plan_orders ADD COLUMN username TEXT")
                    await db.execute("ALTER TABLE plan_orders ADD COLUMN first_name TEXT")
                    await db.execute("ALTER TABLE plan_orders ADD COLUMN last_name TEXT")

                await db.execute("""
                    INSERT INTO plan_orders (
                        order_id, user_id, username, first_name, last_name,
                        plan_id, plan_name, domain, quantity, price, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_data['order_id'],
                    order_data['user_id'],
                    order_data['username'],
                    order_data['first_name'],
                    order_data['last_name'],
                    order_data['plan_id'],
                    order_data['plan_name'],
                    order_data['domain'],
                    order_data['quantity'],
                    order_data['price'],
                    order_data['status'],
                    order_data['created_at']
                ))

                await db.commit()
                return True

        except Exception as e:
            logger.error(f"Error saving plan order: {str(e)}")
            return False

    async def send_plan_order_to_approval_group(self, context: ContextTypes.DEFAULT_TYPE, order_data: Dict) -> None:
        """Send plan order to approval group for immediate approval"""
        try:
            import os
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            # Get approval group ID from environment variables
            approval_group_id = os.getenv('PAYMENT_GROUP_ID')

            if not approval_group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_info = f"👤 **کاربر:** {order_data['first_name']}"
            if order_data['username'] != "N/A":
                user_info += f" (@{order_data['username']})"
            user_info += f"\n🆔 **ID:** `{order_data['user_id']}`"

            message = f"""📦 **درخواست خرید پلن جدید**

{user_info}

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_data['order_id']}`
• پلن: {order_data['plan_name']}
• دامنه: {order_data['domain']}
• تعداد: {order_data['quantity']} عدد
• قیمت: {order_data['price']:,} تومان
• تاریخ: {order_data['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_plan_order_{order_data['order_id']}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_plan_order_{order_data['order_id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send message to approval group
            await context.bot.send_message(
                chat_id=approval_group_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            logger.info(f"Plan order {order_data['order_id']} sent to approval group {approval_group_id}")

        except Exception as e:
            logger.error(f"Error sending plan order to approval group: {str(e)}")

    async def approve_plan_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Approve plan order and send Apple IDs to user"""
        try:
            # Get order details
            order = await self.get_plan_order_by_id(order_id)
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ این سفارش قبلاً پردازش شده است.", show_alert=True)
                return

            # Get available Apple IDs for the domain
            available_apple_ids = db_manager.get_available_apple_ids()
            domain_available = [aid for aid in available_apple_ids if aid.get('Email', '').endswith(f"@{order['domain']}")]

            if len(domain_available) < order['quantity']:
                await update.callback_query.answer(f"❌ اکانت کافی برای {order['domain']} موجود نیست.", show_alert=True)
                return

            # Select required Apple IDs
            selected_apple_ids = domain_available[:order['quantity']]

            # Update order status
            await self.update_plan_order_status(order_id, 'completed')

            # Update order with assigned Apple IDs
            apple_id_emails = [aid['Email'] for aid in selected_apple_ids]
            await self.update_plan_order_apple_ids(order_id, apple_id_emails)

            # Deduct amount from user wallet
            from models.wallet import wallet_model
            await wallet_model.create_purchase_transaction(
                order['user_id'],
                order['price'],
                f"خرید پلن {order['plan_name']} - سفارش {order_id}"
            )

            # Mark Apple IDs as sold with buyer information
            buyer_info = {
                'user_id': order['user_id'],
                'username': order['username'],
                'first_name': order['first_name'],
                'last_name': order['last_name']
            }

            apple_id_list = []
            for apple_id in selected_apple_ids:
                await db_manager.mark_apple_id_as_sold(apple_id['Email'], buyer_info)
                apple_id_list.append({
                    'email': apple_id['Email'],
                    'password': apple_id['Password']
                })

            # Send Apple IDs to user
            await self.send_plan_apple_ids_to_user(context, order, apple_id_list)

            # Send notification to group
            await self.send_plan_approval_to_group(context, order, apple_id_list, update.effective_user.first_name or "ادمین")

            # Update callback message
            admin_name = update.effective_user.first_name or "ادمین"
            new_message = f"""✅ **سفارش پلن تایید شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
🆔 **ID:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

✅ **تایید شده توسط:** {admin_name}
📧 **اکانت‌ها ارسال شدند**"""

            await update.callback_query.edit_message_text(new_message, parse_mode='Markdown')
            await update.callback_query.answer("✅ سفارش پلن تایید شد و اکانت‌ها ارسال شدند.", show_alert=True)

            logger.info(f"Plan order {order_id} approved by admin {update.effective_user.id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "approve_plan_order")

    async def reject_plan_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Reject plan order"""
        try:
            # Get order details
            order = await self.get_plan_order_by_id(order_id)
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ این سفارش قبلاً پردازش شده است.", show_alert=True)
                return

            # Update order status
            await self.update_plan_order_status(order_id, 'rejected')

            # Send rejection message to user
            await self.send_plan_rejection_to_user(context, order)

            # Send notification to group
            await self.send_plan_rejection_to_group(context, order, update.effective_user.first_name or "ادمین")

            # Update callback message
            admin_name = update.effective_user.first_name or "ادمین"
            new_message = f"""❌ **سفارش پلن رد شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
🆔 **ID:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

❌ **رد شده توسط:** {admin_name}"""

            await update.callback_query.edit_message_text(new_message, parse_mode='Markdown')
            await update.callback_query.answer("❌ سفارش پلن رد شد.", show_alert=True)

            logger.info(f"Plan order {order_id} rejected by admin {update.effective_user.id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "reject_plan_order")

    async def get_plan_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get plan order by ID"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT * FROM plan_orders WHERE order_id = ?",
                    (order_id,)
                )
                row = await cursor.fetchone()

                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None

        except Exception as e:
            logger.error(f"Error getting plan order by ID: {str(e)}")
            return None

    async def update_plan_order_status(self, order_id: str, status: str) -> bool:
        """Update plan order status"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                await db.execute(
                    "UPDATE plan_orders SET status = ?, updated_at = ? WHERE order_id = ?",
                    (status, datetime.now().isoformat(), order_id)
                )
                await db.commit()
                return True

        except Exception as e:
            logger.error(f"Error updating plan order status: {str(e)}")
            return False

    async def update_plan_order_apple_ids(self, order_id: str, apple_id_emails: List[str]) -> bool:
        """Update plan order with assigned Apple IDs"""
        try:
            import json
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                await db.execute(
                    "UPDATE plan_orders SET apple_ids_assigned = ? WHERE order_id = ?",
                    (json.dumps(apple_id_emails), order_id)
                )
                await db.commit()
                return True

        except Exception as e:
            logger.error(f"Error updating plan order Apple IDs: {str(e)}")
            return False

    async def send_plan_apple_ids_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id_list: List[Dict]) -> None:
        """Send Apple IDs to user - file if >10, formatted message if <=10"""
        try:
            count = len(apple_id_list)

            if count > 10:
                # Send as file
                await self.send_plan_apple_ids_as_file(context, order, apple_id_list)
            else:
                # Send as formatted message
                await self.send_plan_apple_ids_as_message(context, order, apple_id_list)

            logger.info(f"Plan Apple IDs sent to user {order['user_id']} for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending plan Apple IDs to user: {str(e)}")

    async def send_plan_apple_ids_as_message(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id_list: List[Dict]) -> None:
        """Send Apple IDs as formatted message"""
        try:
            message = f"""✅ **سفارش پلن شما تایید شد!**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

📧 **اکانت‌های Apple ID:**

"""

            for i, apple_id in enumerate(apple_id_list, 1):
                message += f"🔹 **اکانت #{i}:**\n"
                message += f"```\n"
                message += f"ایمیل: {apple_id['email']}\n"
                message += f"پسورد: {apple_id['password']}\n"
                message += f"```\n\n"

            message += """🎉 **تبریک!** اکانت‌های Apple ID شما آماده استفاده هستند.

⚠️ **نکات مهم:**
• اطلاعات اکانت‌ها را در جای امن نگهداری کنید
• از اکانت‌ها فقط برای خریدهای مجاز استفاده کنید
• در صورت بروز مشکل با پشتیبانی تماس بگیرید"""

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error sending plan Apple IDs as message: {str(e)}")

    async def send_plan_apple_ids_as_file(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id_list: List[Dict]) -> None:
        """Send Apple IDs as txt file"""
        try:
            import os

            filename = f"apple_ids_plan_{order['order_id']}.txt"
            filepath = f"temp/{filename}"

            # Create temp directory if not exists
            os.makedirs("temp", exist_ok=True)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"اکانت‌های Apple ID - سفارش پلن {order['order_id']}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"پلن: {order['plan_name']}\n")
                f.write(f"دامنه: {order['domain']}\n")
                f.write(f"تعداد: {order['quantity']} عدد\n")
                f.write(f"قیمت: {order['price']:,} تومان\n")
                f.write(f"تاریخ: {order['created_at'][:16]}\n\n")
                f.write("-" * 50 + "\n\n")

                for i, apple_id in enumerate(apple_id_list, 1):
                    f.write(f"اکانت #{i}:\n")
                    f.write(f"ایمیل: {apple_id['email']}\n")
                    f.write(f"پسورد: {apple_id['password']}\n")
                    f.write("-" * 30 + "\n\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("نکات مهم:\n")
                f.write("• اطلاعات اکانت‌ها را در جای امن نگهداری کنید\n")
                f.write("• از اکانت‌ها فقط برای خریدهای مجاز استفاده کنید\n")
                f.write("• در صورت بروز مشکل با پشتیبانی تماس بگیرید\n")

            # Send file with caption
            caption = f"""✅ **سفارش پلن شما تایید شد!**

📋 **جزئیات:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• تعداد: {order['quantity']} عدد

🎉 **تبریک!** فایل حاوی اطلاعات اکانت‌ها ارسال شد."""

            with open(filepath, 'rb') as f:
                await context.bot.send_document(
                    chat_id=order['user_id'],
                    document=f,
                    filename=filename,
                    caption=caption,
                    parse_mode='Markdown'
                )

            # Clean up
            os.remove(filepath)

        except Exception as e:
            logger.error(f"Error sending plan Apple IDs as file: {str(e)}")

    async def send_plan_rejection_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict) -> None:
        """Send plan rejection message to user"""
        try:
            message = f"""❌ **سفارش پلن شما رد شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

😔 **متأسفانه** سفارش پلن شما توسط ادمین رد شد.

💬 **راه‌های ارتباط:**
• برای اطلاع از دلیل رد سفارش با پشتیبانی تماس بگیرید
• می‌توانید سفارش جدید ثبت کنید"""

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Plan rejection message sent to user {order['user_id']} for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending plan rejection to user: {str(e)}")

    async def get_all_pending_plan_orders(self) -> List[Dict]:
        """Get all pending plan orders"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT * FROM plan_orders WHERE status = 'pending_approval' ORDER BY created_at DESC"
                )
                rows = await cursor.fetchall()

                if rows:
                    columns = [description[0] for description in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                return []

        except Exception as e:
            logger.error(f"Error getting all pending plan orders: {str(e)}")
            return []

    async def send_plan_approval_to_group(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id_list: List[Dict], admin_name: str) -> None:
        """Send plan approval notification to group"""
        try:
            import os

            # Get approval group ID from environment variables
            group_id = os.getenv('PAYMENT_GROUP_ID')

            if not group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""✅ **سفارش پلن تایید شد**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

✅ **تایید شده توسط:** {admin_name}
📧 **اکانت‌ها تحویل داده شد**

⏰ **زمان:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"""

            await context.bot.send_message(
                chat_id=group_id,
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Plan approval notification sent to group for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending plan approval to group: {str(e)}")

    async def send_plan_rejection_to_group(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, admin_name: str) -> None:
        """Send plan rejection notification to group"""
        try:
            import os

            # Get approval group ID from environment variables
            group_id = os.getenv('PAYMENT_GROUP_ID')

            if not group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""❌ **سفارش پلن رد شد**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• پلن: {order['plan_name']}
• دامنه: {order['domain']}
• تعداد: {order['quantity']} عدد
• قیمت: {order['price']:,} تومان

❌ **رد شده توسط:** {admin_name}

⏰ **زمان:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"""

            await context.bot.send_message(
                chat_id=group_id,
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Plan rejection notification sent to group for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending plan rejection to group: {str(e)}")
    
    async def update_user_purchase_stats(self, user_id: int, amount: int) -> None:
        """Update user purchase statistics"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                await db.execute('''
                    UPDATE users SET 
                        total_purchases = total_purchases + 1,
                        total_spent = total_spent + ?,
                        updated_at = ?
                    WHERE user_id = ?
                ''', (amount, datetime.now().isoformat(), user_id))
                await db.commit()
            
        except Exception as e:
            logger.error(f"Failed to update user stats: {str(e)}")
            bot_logger.log_error(e, user_id=user_id, context="Update user stats")

    async def cancel_pending_plan_orders(self, user_id: int) -> None:
        """Cancel all pending plan orders for a user"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Check if table exists first
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='plan_orders'"
                )
                table_exists = await cursor.fetchone()

                if not table_exists:
                    return

                # Update all pending orders to cancelled
                await db.execute(
                    "UPDATE plan_orders SET status = 'cancelled' WHERE user_id = ? AND status = 'pending_approval'",
                    (user_id,)
                )
                await db.commit()

                logger.info(f"Cancelled all pending plan orders for user {user_id}")

        except Exception as e:
            logger.error(f"Error cancelling pending plan orders for user {user_id}: {str(e)}")

# Create global instance
purchase_service = PurchaseService()
