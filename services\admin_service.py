from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from datetime import datetime
import shutil
import os
from logger import bot_logger, logger
from database_manager import db_manager
from error_handler import error_handler
from models.purchase_plans import purchase_plans_model
from models.wallet import wallet_model
from services.backup_service import backup_service
from services.plan_management_service import plan_management_service
from services.user_management_service import user_management_service
from services.settings_service import settings_service
from messages import *

class AdminService:
    def __init__(self):
        logger.info("AdminService initialized successfully")
    
    async def show_bot_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show bot statistics to admin"""
        try:
            logger.info("Admin requested bot stats")

            # Get statistics from Excel database
            all_apple_ids = db_manager.get_all_apple_ids()
            available_ids = db_manager.get_available_apple_ids()

            # Get statistics from SQL database
            import aiosqlite
            async with aiosqlite.connect(db_manager.sql_db_path) as database:
                # User statistics
                cursor = await database.execute("SELECT COUNT(*) FROM users")
                total_users = (await cursor.fetchone())[0]

                cursor = await database.execute("SELECT COUNT(*) FROM users WHERE is_banned = 1")
                banned_users = (await cursor.fetchone())[0] or 0

                active_users = total_users - banned_users

                # Purchase statistics - combine both single orders and plan orders
                # Single orders
                cursor = await database.execute("SELECT COUNT(*), SUM(price) FROM single_orders WHERE status = 'completed'")
                single_data = await cursor.fetchone()
                single_purchases = single_data[0] or 0
                single_revenue = single_data[1] or 0

                # Plan orders
                cursor = await database.execute("SELECT COUNT(*), SUM(price) FROM plan_orders WHERE status = 'completed'")
                plan_data = await cursor.fetchone()
                plan_purchases = plan_data[0] or 0
                plan_revenue = plan_data[1] or 0

                # Legacy purchases table
                cursor = await database.execute("SELECT COUNT(*), SUM(total_price) FROM purchases")
                legacy_data = await cursor.fetchone()
                legacy_purchases = legacy_data[0] or 0
                legacy_revenue = legacy_data[1] or 0

                # Total statistics
                total_purchases = single_purchases + plan_purchases + legacy_purchases
                total_revenue = single_revenue + plan_revenue + legacy_revenue

                # Wallet statistics
                cursor = await database.execute("SELECT SUM(wallet_balance) FROM users")
                total_wallet_balance = (await cursor.fetchone())[0] or 0

                # Pending deposits
                cursor = await database.execute("SELECT COUNT(*) FROM wallet_transactions WHERE transaction_type = 'deposit' AND status = 'pending'")
                pending_deposits = (await cursor.fetchone())[0] or 0

                # Revenue statistics by time periods
                from datetime import datetime, timedelta
                now = datetime.now()

                # Today
                today = now.replace(hour=0, minute=0, second=0, microsecond=0)
                cursor = await database.execute("""
                    SELECT SUM(price) FROM single_orders
                    WHERE status = 'completed' AND created_at >= ?
                    UNION ALL
                    SELECT SUM(price) FROM plan_orders
                    WHERE status = 'completed' AND created_at >= ?
                """, (today.isoformat(), today.isoformat()))
                today_revenues = await cursor.fetchall()
                revenue_today = sum(r[0] or 0 for r in today_revenues)

                # Last 7 days
                seven_days_ago = now - timedelta(days=7)
                cursor = await database.execute("""
                    SELECT SUM(price) FROM single_orders
                    WHERE status = 'completed' AND created_at >= ?
                    UNION ALL
                    SELECT SUM(price) FROM plan_orders
                    WHERE status = 'completed' AND created_at >= ?
                """, (seven_days_ago.isoformat(), seven_days_ago.isoformat()))
                week_revenues = await cursor.fetchall()
                revenue_7d = sum(r[0] or 0 for r in week_revenues)

                # Last 30 days
                thirty_days_ago = now - timedelta(days=30)
                cursor = await database.execute("""
                    SELECT SUM(price) FROM single_orders
                    WHERE status = 'completed' AND created_at >= ?
                    UNION ALL
                    SELECT SUM(price) FROM plan_orders
                    WHERE status = 'completed' AND created_at >= ?
                """, (thirty_days_ago.isoformat(), thirty_days_ago.isoformat()))
                month_revenues = await cursor.fetchall()
                revenue_30d = sum(r[0] or 0 for r in month_revenues)

            stats_message = BOT_STATS_HEADER

            # Apple ID statistics
            stats_message += APPLE_ID_STATS.format(
                total_count=len(all_apple_ids),
                available_count=len(available_ids),
                sold_count=len(all_apple_ids) - len(available_ids)
            )

            # User statistics
            stats_message += f"""
👥 **کاربران:**
• کل کاربران: {total_users:,} نفر
• کاربران فعال: {active_users:,} نفر
• کاربران بن شده: {banned_users:,} نفر
"""

            # Purchase statistics
            stats_message += f"""
💰 **فروش:**
• کل فروش: {total_purchases:,} سفارش
• کل درآمد: {total_revenue:,} تومان
• درآمد امروز: {revenue_today:,} تومان
• درآمد 7 روز: {revenue_7d:,} تومان
• درآمد 30 روز: {revenue_30d:,} تومان
"""

            # Wallet statistics
            stats_message += WALLET_STATS.format(
                total_balance=total_wallet_balance,
                pending_deposits=pending_deposits
            )

            # Domain statistics (replaced country stats)
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            if domain_stats:
                stats_message += "🌐 **آمار بر اساس دامنه ایمیل:**\n"
                # Sort by count (descending)
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains:
                    stats_message += f"• {domain}: {count}\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_bot_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(stats_message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_STATS", "Bot statistics viewed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_bot_stats")

    async def refresh_bot_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh bot statistics"""
        try:
            await update.callback_query.answer("🔄 آمار به‌روزرسانی شد", show_alert=False)

            # Get fresh statistics
            all_apple_ids = db_manager.get_all_apple_ids()

            # Count available and sold Apple IDs
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                # Available if status is empty/None, sold if has value
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            stats_message = f"""📊 **آمار کامل ربات**

📱 **Apple ID ها:**
• مجموع: {len(all_apple_ids)} عدد
• موجود: {available_count} عدد
• فروخته شده: {sold_count} عدد

👥 **کاربران:**
• تعداد کل کاربران: {len(db_manager.get_all_users())} نفر

💰 **مالی:**
• کل فروش: {sold_count} اکانت
• درآمد تقریبی: محاسبه بر اساس قیمت دامنه‌ها
"""

            # Domain statistics (replaced country stats)
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            if domain_stats:
                stats_message += "\n🌐 **آمار بر اساس دامنه ایمیل:**\n"
                # Sort by count (descending)
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains:
                    stats_message += f"• {domain}: {count}\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_bot_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                stats_message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_STATS", "Bot statistics refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_bot_stats")
    
    async def show_apple_id_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show Apple ID management options"""
        try:
            logger.info("Admin accessed Apple ID management")

            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.message.reply_text(NO_APPLE_IDS_IN_DB)
                return

            # Count available and sold accounts
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            # Count domains
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            message = "📱 **مدیریت Apple ID ها**\n\n"
            message += f"📊 **خلاصه آمار:**\n"
            message += f"• مجموع اکانت‌ها: {len(all_apple_ids)} عدد\n"
            message += f"• اکانت‌های موجود: {available_count} عدد\n"
            message += f"• اکانت‌های فروخته شده: {sold_count} عدد\n\n"

            if domain_stats:
                message += "🌐 **آمار دامنه‌ها:**\n"
                # Sort by count (descending) and show top 5
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains[:5]:
                    message += f"• {domain}: {count} عدد\n"

                if len(sorted_domains) > 5:
                    message += f"• ... و {len(sorted_domains) - 5} دامنه دیگر\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_apple_ids")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_APPLE_ID_MGMT", "Apple ID management accessed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_apple_id_management")

    async def refresh_apple_ids(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh Apple ID management"""
        try:
            await update.callback_query.answer("🔄 اطلاعات به‌روزرسانی شد", show_alert=False)

            # Get fresh data
            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.callback_query.edit_message_text(NO_APPLE_IDS_IN_DB)
                return

            # Count available and sold accounts
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            # Count domains
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            message = "📱 **مدیریت Apple ID ها**\n\n"
            message += f"📊 **خلاصه آمار:**\n"
            message += f"• مجموع اکانت‌ها: {len(all_apple_ids)} عدد\n"
            message += f"• اکانت‌های موجود: {available_count} عدد\n"
            message += f"• اکانت‌های فروخته شده: {sold_count} عدد\n\n"

            if domain_stats:
                message += "🌐 **آمار دامنه‌ها:**\n"
                # Sort by count (descending) and show top 5
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains[:5]:
                    message += f"• {domain}: {count} عدد\n"

                if len(sorted_domains) > 5:
                    message += f"• ... و {len(sorted_domains) - 5} دامنه دیگر\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_apple_ids")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_APPLE_ID_MGMT", "Apple ID management refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_apple_ids")

    async def show_plans_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show plans management interface"""
        try:
            await plan_management_service.show_plans_management(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_plans_management")
    
    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user management options"""
        try:
            await user_management_service.show_user_management(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_management")
    
    async def show_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show bot settings"""
        try:
            await settings_service.show_settings_menu(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_settings")
    
    async def show_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show recent logs to admin"""
        try:
            import os
            from dotenv import load_dotenv

            # Load environment variables to get log file name
            load_dotenv()
            log_file = os.getenv('LOG_FILE', 'botlog.txt')

            if not os.path.exists(log_file):
                await update.message.reply_text(f"📋 فایل لاگ یافت نشد: {log_file}")
                return

            # Read last 25 lines of log file
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_logs = lines[-25:] if len(lines) > 25 else lines

            if not recent_logs:
                await update.message.reply_text("📋 فایل لاگ خالی است")
                return

            log_message = f"📋 **آخرین لاگ ها ({log_file}):**\n\n```\n"
            log_message += "".join(recent_logs)
            log_message += "\n```"

            # Split message if too long for Telegram
            if len(log_message) > 4000:
                log_message = log_message[:3900] + "...\n\n[لاگ کامل در فایل موجود است]\n```"

            await update.message.reply_text(log_message, parse_mode='Markdown')
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_LOGS", f"Logs viewed from {log_file}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_logs")
    
    async def show_pending_deposits(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show all pending transactions (deposits, single orders, plan orders)"""
        try:
            # Get pending deposits
            pending_deposits = await wallet_model.get_pending_deposits()

            # Get pending single orders
            from services.single_purchase_service import single_purchase_service
            pending_single_orders = await single_purchase_service.get_all_pending_orders()

            # Get pending plan orders
            from services.purchase_service import purchase_service
            pending_plan_orders = await purchase_service.get_all_pending_plan_orders()

            # Check if any pending transactions exist
            total_pending = len(pending_deposits) + len(pending_single_orders) + len(pending_plan_orders)

            if total_pending == 0:
                await update.message.reply_text(NO_PENDING_DEPOSITS)
                return

            message = PENDING_DEPOSITS_HEADER

            # Add pending deposits
            if pending_deposits:
                message += "💰 **واریزهای در انتظار:**\n\n"
                for deposit in pending_deposits:
                    user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{deposit['username']}" if deposit.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{deposit['user_id']}`
💵 **مبلغ:** {deposit['amount']:,} تومان
🆔 **شماره تراکنش:** {deposit['id']}
📅 **تاریخ:** {deposit['created_at'][:16].replace('T', ' ')}
──────────────────────────────

"""

            # Add pending single orders
            if pending_single_orders:
                message += "🛒 **خریدهای تکی در انتظار:**\n\n"
                for order in pending_single_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{order['user_id']}`
🛒 **نوع:** خرید تکی
📧 **دامنه:** {order['domain']}
💵 **قیمت:** {order['price']:,} تومان
🆔 **شماره سفارش:** {order['order_id']}
📅 **تاریخ:** {order['created_at'][:16]}
──────────────────────────────

"""

            # Add pending plan orders
            if pending_plan_orders:
                message += "📦 **خریدهای پلن در انتظار:**\n\n"
                for order in pending_plan_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{order['user_id']}`
📦 **نوع:** خرید پلن
📧 **پلن:** {order['plan_name']}
🌐 **دامنه:** {order['domain']}
🔢 **تعداد:** {order['quantity']} عدد
💵 **قیمت:** {order['price']:,} تومان
🆔 **شماره سفارش:** {order['order_id']}
📅 **تاریخ:** {order['created_at'][:16]}
──────────────────────────────

"""

            # Add glass buttons for individual transactions
            keyboard = []

            # Add pending deposits as individual buttons
            if pending_deposits:
                for deposit in pending_deposits:
                    user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"💰 تراکنش {deposit['transaction_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_deposit_{deposit['transaction_id']}")])

            # Add pending single orders as individual buttons
            if pending_single_orders:
                for order in pending_single_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"🛒 سفارش {order['order_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_single_order_{order['order_id']}")])

            # Add pending plan orders as individual buttons
            if pending_plan_orders:
                for order in pending_plan_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"📦 پلن {order['order_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_plan_order_{order['order_id']}")])

            # Add back button
            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="admin_panel")])

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_PENDING_TRANSACTIONS", f"Viewed {total_pending} pending transactions")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_pending_transactions")

    async def view_deposit_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, transaction_id: int) -> None:
        """Show deposit details with approve/reject buttons"""
        try:
            deposit = await wallet_model.get_transaction_by_id(transaction_id)

            if not deposit:
                await update.callback_query.answer("❌ تراکنش یافت نشد.", show_alert=True)
                return

            user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{deposit['username']}" if deposit.get('username') else "بدون نام کاربری"

            message = f"""💰 **جزئیات تراکنش واریز**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {deposit['user_id']}

💳 **جزئیات تراکنش:**
• شماره تراکنش: `{deposit['transaction_id']}`
• مبلغ: {deposit['amount']:,} تومان
• تاریخ: {deposit['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_deposit_{transaction_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_deposit_{transaction_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="pending_deposits")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_deposit_details")

    async def manage_single_orders(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage pending single orders with glass buttons"""
        try:
            from services.single_purchase_service import single_purchase_service
            pending_orders = await single_purchase_service.get_all_pending_orders()

            if not pending_orders:
                await update.callback_query.edit_message_text("✅ هیچ سفارش تکی در انتظار وجود ندارد.")
                return

            message = "🛒 **مدیریت خریدهای تکی در انتظار**\n\n"
            keyboard = []

            for order in pending_orders:
                user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                if not user_name:
                    user_name = "نام نامشخص"

                # Create glass button for each order
                button_text = f"🔸 {user_name} - {order['domain']} ({order['price']:,}ت)"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_single_order_{order['order_id']}")])

            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="pending_deposits")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manage_single_orders")

    async def manage_plan_orders(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage pending plan orders with glass buttons"""
        try:
            from services.purchase_service import purchase_service
            pending_orders = await purchase_service.get_all_pending_plan_orders()

            if not pending_orders:
                await update.callback_query.edit_message_text("✅ هیچ سفارش پلن در انتظار وجود ندارد.")
                return

            message = "📦 **مدیریت خریدهای پلن در انتظار**\n\n"
            keyboard = []

            for order in pending_orders:
                user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                if not user_name:
                    user_name = "نام نامشخص"

                # Create glass button for each order
                button_text = f"🔸 {user_name} - {order['plan_name']} ({order['price']:,}ت)"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_plan_order_{order['order_id']}")])

            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="pending_deposits")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manage_plan_orders")

    async def view_single_order_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Show single order details with approve/reject buttons"""
        try:
            from services.single_purchase_service import single_purchase_service
            order = await single_purchase_service.get_order_by_id(order_id)

            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""🛒 **جزئیات سفارش تکی**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {order['user_id']}

🌐 **دامنه:** {order['domain']}
💰 **قیمت:** {order['price']:,} تومان
📅 **تاریخ:** {order['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_single_order_{order_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_single_order_{order_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="manage_single_orders")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_single_order_details")

    async def view_plan_order_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Show plan order details with approve/reject buttons"""
        try:
            from services.purchase_service import purchase_service
            order = await purchase_service.get_plan_order_by_id(order_id)

            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""📦 **جزئیات سفارش پلن**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {order['user_id']}

📋 **پلن:** {order['plan_name']}
🌐 **دامنه:** {order['domain']}
🔢 **تعداد:** {order['quantity']} عدد
💰 **قیمت:** {order['price']:,} تومان
📅 **تاریخ:** {order['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_plan_order_{order_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_plan_order_{order_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="manage_plan_orders")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_plan_order_details")

    async def manual_backup(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Perform manual backup"""
        try:
            # Import backup_service here to avoid circular import issues
            from services.backup_service import backup_service

            if backup_service:
                await update.message.reply_text(MANUAL_BACKUP_START)

                json_path, sql_path, excel_paths = await backup_service.manual_backup()

                # Send backup files
                await backup_service.send_backup_files_to_admin(json_path, sql_path, "manual", excel_paths)

                await update.message.reply_text(MANUAL_BACKUP_SUCCESS)
                bot_logger.log_admin_action(update.effective_user.id, "MANUAL_BACKUP", "Manual backup completed")
            else:
                # Try to create backup manually if service is not available
                await self.create_manual_backup_fallback(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manual_backup")

    async def create_manual_backup_fallback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Fallback method for manual backup when service is unavailable"""
        try:
            await update.message.reply_text("📁 در حال ایجاد بکاپ (حالت fallback)...")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create JSON backup
            json_backup_path = await db_manager.backup_to_json()

            # Create SQL backup
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)
            sql_backup_filename = f"database_backup_{timestamp}.db"
            sql_backup_path = os.path.join(backup_dir, sql_backup_filename)
            shutil.copy2(db_manager.sql_db_path, sql_backup_path)

            # Create Excel backups
            excel_backup_paths = []
            database_dir = "database"
            if os.path.exists(database_dir):
                import glob
                excel_files = glob.glob(os.path.join(database_dir, "*.xlsx"))
                for excel_file in excel_files:
                    if os.path.exists(excel_file):
                        original_name = os.path.basename(excel_file)
                        name_without_ext = os.path.splitext(original_name)[0]
                        backup_filename = f"{name_without_ext}_backup_{timestamp}.xlsx"
                        backup_path = os.path.join(backup_dir, backup_filename)
                        shutil.copy2(excel_file, backup_path)
                        excel_backup_paths.append(backup_path)

            # Send files to admin
            user_id = update.effective_user.id

            message = f"📁 **بکاپ دستی**\n\n📅 تاریخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}\n🆔 شناسه: {timestamp}\n\n"
            message += f"📄 **فایل‌های ضمیمه:**\n• 📄 دیتابیس JSON\n• 🗄️ دیتابیس SQL\n"
            if excel_backup_paths:
                message += f"• 📊 فایل‌های اکسل ({len(excel_backup_paths)} فایل)\n"
            message += f"\n✅ بکاپ با موفقیت ایجاد شد"

            await update.message.reply_text(message, parse_mode='Markdown')

            # Send JSON file
            if os.path.exists(json_backup_path):
                with open(json_backup_path, 'rb') as json_file:
                    await context.bot.send_document(
                        chat_id=user_id,
                        document=json_file,
                        filename=os.path.basename(json_backup_path),
                        caption="📄 بکاپ JSON دیتابیس"
                    )

            # Send SQL file
            if os.path.exists(sql_backup_path):
                with open(sql_backup_path, 'rb') as sql_file:
                    await context.bot.send_document(
                        chat_id=user_id,
                        document=sql_file,
                        filename=os.path.basename(sql_backup_path),
                        caption="🗄️ بکاپ SQL دیتابیس"
                    )

            # Send Excel backup files
            for excel_path in excel_backup_paths:
                if os.path.exists(excel_path):
                    with open(excel_path, 'rb') as excel_file:
                        await context.bot.send_document(
                            chat_id=user_id,
                            document=excel_file,
                            filename=os.path.basename(excel_path),
                            caption="📊 بکاپ فایل اکسل"
                        )

            bot_logger.log_admin_action(user_id, "MANUAL_BACKUP_FALLBACK", f"Manual backup fallback completed: {timestamp}")

        except Exception as e:
            logger.error(f"Manual backup fallback failed: {str(e)}")
            await update.message.reply_text("❌ خطا در ایجاد بکاپ")

    async def refresh_data(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh database data"""
        try:
            # Reinitialize database connections
            db_manager.initialize_excel_database()
            await db_manager.initialize_sql_database()

            await update.message.reply_text(DATABASE_REFRESHED)
            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_DATA", "Databases refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_data")

    async def show_accounts_from_database(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show accounts from database - admin can specify range"""
        try:
            message = """📱 **نمایش اکانت‌ها از دیتابیس**

📋 **راهنما:**
• برای نمایش یک اکانت: عدد تکی (مثال: `35`)
• برای نمایش چند اکانت: محدوده (مثال: `30-45`)

⚠️ **نکته:** اگر تعداد بیش از 10 باشد، به صورت فایل txt ارسال می‌شود.

🔢 **لطفاً شماره یا محدوده مورد نظر را وارد کنید:**"""

            context.user_data['waiting_for_account_range'] = True
            await update.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_admin_action(
                update.effective_user.id,
                "REQUEST_ACCOUNT_DISPLAY",
                "Admin requested account display from database"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_accounts_from_database")

    async def process_account_range(self, update: Update, context: ContextTypes.DEFAULT_TYPE, range_text: str) -> None:
        """Process account range request from admin"""
        try:
            context.user_data.pop('waiting_for_account_range', None)

            # Parse range
            if '-' in range_text:
                # Range format: 30-45
                try:
                    start, end = map(int, range_text.split('-'))
                    if start > end:
                        start, end = end, start
                except ValueError:
                    await update.message.reply_text("❌ فرمت نامعتبر. مثال صحیح: `30-45`", parse_mode='Markdown')
                    return
            else:
                # Single number: 35
                try:
                    start = end = int(range_text)
                except ValueError:
                    await update.message.reply_text("❌ فرمت نامعتبر. مثال صحیح: `35`", parse_mode='Markdown')
                    return

            # Get all Apple IDs from database
            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.message.reply_text("❌ هیچ اکانتی در دیتابیس یافت نشد.")
                return

            # Validate range
            if start < 1 or end > len(all_apple_ids):
                await update.message.reply_text(f"❌ محدوده نامعتبر. تعداد کل اکانت‌ها: {len(all_apple_ids)}")
                return

            # Get requested accounts (convert to 0-based index)
            requested_accounts = all_apple_ids[start-1:end]
            count = len(requested_accounts)

            if count > 10:
                # Send as file
                await self.send_accounts_as_file(update, context, requested_accounts, start, end)
            else:
                # Send as formatted message
                await self.send_accounts_as_message(update, context, requested_accounts, start, end)

            bot_logger.log_admin_action(
                update.effective_user.id,
                "VIEW_ACCOUNTS_RANGE",
                f"Viewed accounts {start}-{end} ({count} accounts)"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_account_range")

    async def send_accounts_as_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, accounts: list, start: int, end: int) -> None:
        """Send accounts as formatted message"""
        try:
            message = f"📱 **اکانت‌های {start} تا {end}**\n\n"

            for i, account in enumerate(accounts, start):
                status_raw = account.get('Status')
                if status_raw is None:
                    status = ''
                else:
                    status = str(status_raw).strip()
                status_emoji = "🔴" if status.lower() in ['s', 'sold', 'فروخته شده'] else "🟢"

                message += f"{status_emoji} **اکانت #{i}:**\n"
                message += f"```\n"
                message += f"ایمیل: {account.get('Email', account.get('email', 'N/A'))}\n"
                message += f"پسورد: {account.get('Password', account.get('password', 'N/A'))}\n"
                message += f"وضعیت: {status or 'موجود'}\n"

                if status.lower() in ['s', 'sold', 'فروخته شده']:
                    message += f"خریدار: {account.get('first_name', 'N/A')} {account.get('last_name', '')}\n"
                    message += f"تلگرام: @{account.get('telegram_id', 'N/A')}\n"
                    message += f"تاریخ خرید: {account.get('purchase_date', 'N/A')}\n"

                message += f"```\n"
                message += "──────────────────────────────\n"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            await error_handler.handle_error(update, context, e, "send_accounts_as_message")

    async def send_accounts_as_file(self, update: Update, context: ContextTypes.DEFAULT_TYPE, accounts: list, start: int, end: int) -> None:
        """Send accounts as txt file"""
        try:
            filename = f"accounts_{start}_{end}.txt"
            filepath = f"temp/{filename}"

            # Create temp directory if not exists
            os.makedirs("temp", exist_ok=True)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"اکانت‌های {start} تا {end}\n")
                f.write("=" * 50 + "\n\n")

                for i, account in enumerate(accounts, start):
                    status_raw = account.get('Status')
                    if status_raw is None:
                        status = ''
                    else:
                        status = str(status_raw).strip()
                    status_text = "فروخته شده" if status.lower() in ['s', 'sold', 'فروخته شده'] else "موجود"

                    f.write(f"اکانت #{i}:\n")
                    f.write(f"ایمیل: {account.get('Email', account.get('email', 'N/A'))}\n")
                    f.write(f"پسورد: {account.get('Password', account.get('password', 'N/A'))}\n")
                    f.write(f"وضعیت: {status_text}\n")

                    if status.lower() in ['s', 'sold', 'فروخته شده']:
                        f.write(f"خریدار: {account.get('first_name', 'N/A')} {account.get('last_name', '')}\n")
                        f.write(f"تلگرام: @{account.get('telegram_id', 'N/A')}\n")
                        f.write(f"تاریخ خرید: {account.get('purchase_date', 'N/A')}\n")

                    f.write("-" * 30 + "\n\n")

            # Send file
            with open(filepath, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=filename,
                    caption=f"📱 **اکانت‌های {start} تا {end}**\n\n📊 تعداد: {len(accounts)} اکانت"
                )

            # Clean up
            os.remove(filepath)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "send_accounts_as_file")
