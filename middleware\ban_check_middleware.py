from telegram import Update
from telegram.ext import ContextTypes
import aiosqlite
from database_manager import db_manager
from logger import logger

class BanCheckMiddleware:
    def __init__(self):
        self.sql_db_path = db_manager.sql_db_path
        logger.info("BanCheckMiddleware initialized successfully")

    async def check_user_ban_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Check if user is banned. Returns True if user is banned, False otherwise.
        """
        try:
            user_id = update.effective_user.id
            
            # Skip ban check for admins
            from utils.auth import AuthUtils
            auth_utils = AuthUtils()
            if await auth_utils.is_admin(user_id):
                return False
            
            # Check ban status
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT is_banned FROM users WHERE user_id = ?",
                    (user_id,)
                )
                result = await cursor.fetchone()
                
                if result and result[0]:  # User is banned
                    await self.send_ban_message(update, context)
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking ban status: {str(e)}")
            return False

    async def send_ban_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send ban message to user and remove all keyboards"""
        try:
            ban_message = """🚫 **دسترسی محدود شده**

متأسفانه دسترسی شما به این ربات توسط مدیریت محدود شده است.

📞 **برای رفع محدودیت:**
• با پشتیبانی تماس بگیرید
• دلیل محدودیت را استعلام کنید

⚠️ **توجه:** تا رفع محدودیت، امکان استفاده از ربات وجود ندارد."""

            # Remove any existing keyboard and send ban message
            if update.message:
                await update.message.reply_text(
                    ban_message,
                    parse_mode='Markdown',
                    reply_markup=None  # Remove keyboard
                )
            elif update.callback_query:
                await update.callback_query.edit_message_text(
                    ban_message,
                    parse_mode='Markdown',
                    reply_markup=None  # Remove inline keyboard
                )
                await update.callback_query.answer("🚫 دسترسی محدود شده", show_alert=True)
                
        except Exception as e:
            logger.error(f"Error sending ban message: {str(e)}")

# Global instance
ban_check_middleware = BanCheckMiddleware()
