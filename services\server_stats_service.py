from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import psutil
import os
import platform
import socket
from datetime import datetime, timedelta
import aiosqlite
from typing import Dict

from logger import logger, bot_logger
from database_manager import db_manager
from error_handler import error_handler
from utils.auth import AuthUtils

class ServerStatsService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("ServerStatsService initialized successfully")

    async def show_server_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show server statistics to admin"""
        try:
            user_id = update.effective_user.id
            
            # Only admins can access this
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            # Get system information
            system_info = self.get_system_info()
            
            # Get resource usage
            resource_usage = self.get_resource_usage()
            
            # Get disk usage
            disk_usage = self.get_disk_usage()
            
            # Get network info
            network_info = self.get_network_info()
            
            # Get database stats
            db_stats = await self.get_database_stats()
            
            # Get bot uptime
            bot_uptime = self.get_bot_uptime()

            message = f"""🖥️ **آمار و اطلاعات سرور**

🔧 **مشخصات سیستم:**
• سیستم عامل: {system_info['os']}
• معماری: {system_info['architecture']}
• نام میزبان: {system_info['hostname']}
• پردازنده: {system_info['cpu_model']}
• تعداد هسته: {system_info['cpu_cores']} هسته

💾 **منابع سیستم:**
• استفاده از CPU: {resource_usage['cpu_percent']:.1f}%
• استفاده از RAM: {resource_usage['memory_percent']:.1f}%
• RAM آزاد: {resource_usage['memory_available']:.1f} GB
• RAM کل: {resource_usage['memory_total']:.1f} GB

💿 **فضای دیسک:**
• فضای کل: {disk_usage['total']:.1f} GB
• فضای استفاده شده: {disk_usage['used']:.1f} GB
• فضای آزاد: {disk_usage['free']:.1f} GB
• درصد استفاده: {disk_usage['percent']:.1f}%

🌐 **شبکه:**
• آدرس IP محلی: {network_info['local_ip']}
• آدرس IP عمومی: {network_info['public_ip']}

📊 **آمار دیتابیس:**
• تعداد کل جداول: {db_stats['total_tables']}
• تعداد کل رکوردها: {db_stats['total_records']:,}
• حجم دیتابیس: {db_stats['db_size']:.2f} MB

⏰ **زمان فعالیت:**
• شروع ربات: {bot_uptime['start_time']}
• مدت فعالیت: {bot_uptime['uptime']}

🔄 **آخرین به‌روزرسانی:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("🔄 به‌روزرسانی", callback_data="refresh_server_stats")],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="admin_panel")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if update.message:
                await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            else:
                await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            
            bot_logger.log_admin_action(user_id, "SERVER_STATS", "Viewed server statistics")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_server_stats")

    def get_system_info(self) -> Dict:
        """Get system information"""
        try:
            return {
                'os': f"{platform.system()} {platform.release()}",
                'architecture': platform.machine(),
                'hostname': socket.gethostname(),
                'cpu_model': platform.processor() or "نامشخص",
                'cpu_cores': psutil.cpu_count(logical=True)
            }
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            return {
                'os': 'نامشخص',
                'architecture': 'نامشخص',
                'hostname': 'نامشخص',
                'cpu_model': 'نامشخص',
                'cpu_cores': 0
            }

    def get_resource_usage(self) -> Dict:
        """Get resource usage information"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_total = memory.total / (1024**3)  # Convert to GB
            memory_available = memory.available / (1024**3)  # Convert to GB
            memory_percent = memory.percent
            
            return {
                'cpu_percent': cpu_percent,
                'memory_total': memory_total,
                'memory_available': memory_available,
                'memory_percent': memory_percent
            }
        except Exception as e:
            logger.error(f"Error getting resource usage: {str(e)}")
            return {
                'cpu_percent': 0,
                'memory_total': 0,
                'memory_available': 0,
                'memory_percent': 0
            }

    def get_disk_usage(self) -> Dict:
        """Get disk usage information"""
        try:
            # Get disk usage for current directory
            disk_usage = psutil.disk_usage('.')
            
            total = disk_usage.total / (1024**3)  # Convert to GB
            used = disk_usage.used / (1024**3)  # Convert to GB
            free = disk_usage.free / (1024**3)  # Convert to GB
            percent = (used / total) * 100
            
            return {
                'total': total,
                'used': used,
                'free': free,
                'percent': percent
            }
        except Exception as e:
            logger.error(f"Error getting disk usage: {str(e)}")
            return {
                'total': 0,
                'used': 0,
                'free': 0,
                'percent': 0
            }

    def get_network_info(self) -> Dict:
        """Get network information"""
        try:
            # Get local IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # Try to get public IP (simplified)
            try:
                import requests
                public_ip = requests.get('https://api.ipify.org', timeout=5).text
            except:
                public_ip = "نامشخص"
            
            return {
                'local_ip': local_ip,
                'public_ip': public_ip
            }
        except Exception as e:
            logger.error(f"Error getting network info: {str(e)}")
            return {
                'local_ip': 'نامشخص',
                'public_ip': 'نامشخص'
            }

    async def get_database_stats(self) -> Dict:
        """Get database statistics"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Get table count
                cursor = await db.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                total_tables = (await cursor.fetchone())[0]
                
                # Get total records from main tables
                total_records = 0
                tables = ['users', 'single_orders', 'plan_orders', 'wallet_transactions', 'purchases']
                
                for table in tables:
                    try:
                        cursor = await db.execute(f"SELECT COUNT(*) FROM {table}")
                        count = (await cursor.fetchone())[0]
                        total_records += count
                    except:
                        pass  # Table might not exist
                
                # Get database file size
                db_size = os.path.getsize(db_manager.sql_db_path) / (1024**2)  # Convert to MB
                
                return {
                    'total_tables': total_tables,
                    'total_records': total_records,
                    'db_size': db_size
                }
        except Exception as e:
            logger.error(f"Error getting database stats: {str(e)}")
            return {
                'total_tables': 0,
                'total_records': 0,
                'db_size': 0
            }

    def get_bot_uptime(self) -> Dict:
        """Get bot uptime information"""
        try:
            # Get process start time
            process = psutil.Process()
            start_time = datetime.fromtimestamp(process.create_time())
            uptime = datetime.now() - start_time
            
            # Format uptime
            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            uptime_str = f"{days} روز، {hours} ساعت، {minutes} دقیقه"
            
            return {
                'start_time': start_time.strftime('%Y/%m/%d %H:%M:%S'),
                'uptime': uptime_str
            }
        except Exception as e:
            logger.error(f"Error getting bot uptime: {str(e)}")
            return {
                'start_time': 'نامشخص',
                'uptime': 'نامشخص'
            }

# Global instance
server_stats_service = ServerStatsService()
