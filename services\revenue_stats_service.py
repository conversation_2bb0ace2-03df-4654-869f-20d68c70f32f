import aiosqlite
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List
import asyncio

from logger import logger
from database_manager import db_manager

class RevenueStatsService:
    def __init__(self):
        self.sql_db_path = db_manager.sql_db_path
        logger.info("RevenueStatsService initialized successfully")

    async def update_daily_revenue_stats(self) -> None:
        """Update daily revenue statistics"""
        try:
            today = datetime.now().date().isoformat()
            
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Calculate today's revenue
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                
                # Single orders revenue
                cursor = await db.execute("""
                    SELECT SUM(price) FROM single_orders 
                    WHERE status = 'completed' AND created_at >= ?
                """, (today_start.isoformat(),))
                single_revenue = (await cursor.fetchone())[0] or 0
                
                # Plan orders revenue
                cursor = await db.execute("""
                    SELECT SUM(price) FROM plan_orders 
                    WHERE status = 'completed' AND created_at >= ?
                """, (today_start.isoformat(),))
                plan_revenue = (await cursor.fetchone())[0] or 0
                
                daily_revenue = single_revenue + plan_revenue
                
                # Calculate monthly revenue (current month)
                month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                
                cursor = await db.execute("""
                    SELECT SUM(price) FROM single_orders 
                    WHERE status = 'completed' AND created_at >= ?
                    UNION ALL
                    SELECT SUM(price) FROM plan_orders 
                    WHERE status = 'completed' AND created_at >= ?
                """, (month_start.isoformat(), month_start.isoformat()))
                month_revenues = await cursor.fetchall()
                monthly_revenue = sum(r[0] or 0 for r in month_revenues)
                
                # Calculate yearly revenue (current year)
                year_start = datetime.now().replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
                
                cursor = await db.execute("""
                    SELECT SUM(price) FROM single_orders 
                    WHERE status = 'completed' AND created_at >= ?
                    UNION ALL
                    SELECT SUM(price) FROM plan_orders 
                    WHERE status = 'completed' AND created_at >= ?
                """, (year_start.isoformat(), year_start.isoformat()))
                year_revenues = await cursor.fetchall()
                yearly_revenue = sum(r[0] or 0 for r in year_revenues)
                
                # Insert or update revenue stats
                current_time = datetime.now().isoformat()
                
                await db.execute("""
                    INSERT OR REPLACE INTO revenue_stats 
                    (date, daily_revenue, monthly_revenue, yearly_revenue, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (today, daily_revenue, monthly_revenue, yearly_revenue, current_time, current_time))
                
                await db.commit()
                
                logger.info(f"Updated revenue stats for {today}: Daily={daily_revenue:,}, Monthly={monthly_revenue:,}, Yearly={yearly_revenue:,}")
                
        except Exception as e:
            logger.error(f"Error updating daily revenue stats: {str(e)}")

    async def get_revenue_stats_by_period(self, days: int) -> Dict:
        """Get revenue statistics for a specific period"""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute("""
                    SELECT date, daily_revenue FROM revenue_stats 
                    WHERE date >= ? AND date <= ?
                    ORDER BY date
                """, (start_date.isoformat(), end_date.isoformat()))
                
                results = await cursor.fetchall()
                
                total_revenue = sum(row[1] for row in results)
                daily_stats = {row[0]: row[1] for row in results}
                
                return {
                    'period_days': days,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'total_revenue': total_revenue,
                    'daily_stats': daily_stats,
                    'average_daily': total_revenue / days if days > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Error getting revenue stats for {days} days: {str(e)}")
            return {
                'period_days': days,
                'start_date': '',
                'end_date': '',
                'total_revenue': 0,
                'daily_stats': {},
                'average_daily': 0
            }

    async def get_monthly_revenue_stats(self, months: int = 12) -> List[Dict]:
        """Get monthly revenue statistics"""
        try:
            monthly_stats = []
            
            for i in range(months):
                # Calculate the start of the month
                current_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                target_month = current_date - timedelta(days=30 * i)
                month_start = target_month.replace(day=1)
                
                # Calculate the end of the month
                if target_month.month == 12:
                    month_end = target_month.replace(year=target_month.year + 1, month=1, day=1) - timedelta(days=1)
                else:
                    month_end = target_month.replace(month=target_month.month + 1, day=1) - timedelta(days=1)
                
                async with aiosqlite.connect(self.sql_db_path) as db:
                    # Get revenue for this month
                    cursor = await db.execute("""
                        SELECT SUM(price) FROM single_orders 
                        WHERE status = 'completed' AND created_at >= ? AND created_at <= ?
                        UNION ALL
                        SELECT SUM(price) FROM plan_orders 
                        WHERE status = 'completed' AND created_at >= ? AND created_at <= ?
                    """, (month_start.isoformat(), month_end.isoformat(), 
                          month_start.isoformat(), month_end.isoformat()))
                    
                    revenues = await cursor.fetchall()
                    month_revenue = sum(r[0] or 0 for r in revenues)
                    
                    monthly_stats.append({
                        'year': target_month.year,
                        'month': target_month.month,
                        'month_name': target_month.strftime('%B %Y'),
                        'month_name_fa': self.get_persian_month_name(target_month),
                        'revenue': month_revenue,
                        'start_date': month_start.date().isoformat(),
                        'end_date': month_end.date().isoformat()
                    })
            
            return monthly_stats
            
        except Exception as e:
            logger.error(f"Error getting monthly revenue stats: {str(e)}")
            return []

    def get_persian_month_name(self, date: datetime) -> str:
        """Get Persian month name"""
        persian_months = {
            1: 'ژانویه', 2: 'فوریه', 3: 'مارس', 4: 'آوریل',
            5: 'مه', 6: 'ژوئن', 7: 'ژوئیه', 8: 'اوت',
            9: 'سپتامبر', 10: 'اکتبر', 11: 'نوامبر', 12: 'دسامبر'
        }
        return f"{persian_months.get(date.month, '')} {date.year}"

    async def get_yearly_revenue_stats(self, years: int = 5) -> List[Dict]:
        """Get yearly revenue statistics"""
        try:
            yearly_stats = []
            current_year = datetime.now().year
            
            for i in range(years):
                target_year = current_year - i
                year_start = datetime(target_year, 1, 1)
                year_end = datetime(target_year, 12, 31, 23, 59, 59)
                
                async with aiosqlite.connect(self.sql_db_path) as db:
                    cursor = await db.execute("""
                        SELECT SUM(price) FROM single_orders 
                        WHERE status = 'completed' AND created_at >= ? AND created_at <= ?
                        UNION ALL
                        SELECT SUM(price) FROM plan_orders 
                        WHERE status = 'completed' AND created_at >= ? AND created_at <= ?
                    """, (year_start.isoformat(), year_end.isoformat(),
                          year_start.isoformat(), year_end.isoformat()))
                    
                    revenues = await cursor.fetchall()
                    year_revenue = sum(r[0] or 0 for r in revenues)
                    
                    yearly_stats.append({
                        'year': target_year,
                        'revenue': year_revenue,
                        'start_date': year_start.date().isoformat(),
                        'end_date': year_end.date().isoformat()
                    })
            
            return yearly_stats
            
        except Exception as e:
            logger.error(f"Error getting yearly revenue stats: {str(e)}")
            return []

    async def start_daily_stats_updater(self) -> None:
        """Start the daily stats updater task"""
        try:
            while True:
                await self.update_daily_revenue_stats()
                # Wait for 1 hour before next update
                await asyncio.sleep(3600)
                
        except Exception as e:
            logger.error(f"Error in daily stats updater: {str(e)}")

# Global instance
revenue_stats_service = RevenueStatsService()
