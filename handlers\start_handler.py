from telegram import Update, <PERSON><PERSON><PERSON><PERSON><PERSON>Markup, KeyboardButton, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ove
from telegram.ext import ContextTypes
from datetime import datetime
from logger import bot_logger, logger
from messages import WELCOME_ADMIN, WELCOME_USER_NEW, DEFAULT_BTN_APPLE_IDS, DEFAULT_BTN_PRICES, DEFAULT_BTN_PURCHASE_PLANS, DEFAULT_BTN_WALLET, DEFAULT_BTN_SUPPORT, DEFAULT_BTN_HELP, DEFAULT_BTN_SINGLE_PURCHASE
from error_handler import error_handler
from utils.auth import AuthUtils
from utils.forced_join_middleware import forced_join_middleware
from database_manager import db_manager
from services.user_service import user_service
from services.purchase_service import purchase_service
from services.wallet_service import wallet_service
from services.settings_service import settings_service

class StartHandler:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("StartHandler initialized successfully")
    
    async def handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"

            logger.info(f"Start command received from user {user_id} (@{username})")
            bot_logger.log_user_action(user_id, username, "START_COMMAND")

            # Save/update user in database
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }

            await db_manager.create_user(user_data)

            if await self.auth_utils.is_admin(user_id):
                # Admin welcome message
                # Check if this is from callback query or message
                if hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.edit_message_text(
                        WELCOME_ADMIN,
                        reply_markup=self._get_admin_keyboard(user_id)
                    )
                else:
                    await update.message.reply_text(
                        WELCOME_ADMIN,
                        reply_markup=self._get_admin_keyboard(user_id)
                    )
                bot_logger.log_admin_action(user_id, "ADMIN_LOGIN", f"Admin {username} logged in")
                logger.info(f"Admin {user_id} logged in successfully")

            else:
                # Check maintenance mode
                settings = settings_service.load_settings()
                maintenance_mode = settings.get("general", {}).get("maintenance_mode", False)

                if maintenance_mode:
                    # In maintenance mode, remove keyboard completely
                    maintenance_text = "🔧 **ربات در حال تعمیر است**\n\nلطفاً منتظر باشید و بعداً دوباره دستور /start را ارسال کنید."

                    # Check if this is from callback query or message
                    if hasattr(update, 'callback_query') and update.callback_query:
                        await update.callback_query.edit_message_text(
                            maintenance_text,
                            parse_mode='Markdown'
                        )
                    else:
                        await update.message.reply_text(
                            maintenance_text,
                            parse_mode='Markdown',
                            reply_markup=ReplyKeyboardRemove()
                        )
                    bot_logger.log_user_action(user_id, username, "MAINTENANCE_ACCESS", "User tried to access during maintenance")
                    return

                # Check forced join channels membership
                if not await forced_join_middleware.check_user_access(update, context):
                    # User doesn't have access due to missing channel memberships
                    bot_logger.log_user_action(user_id, username, "FORCED_JOIN_BLOCKED", "User blocked due to missing channel memberships")
                    return

                # Get user information for welcome message
                user_info = await self._get_user_welcome_info(user_data)

                # Regular user welcome message with detailed info
                welcome_message = WELCOME_USER_NEW.format(
                    user_id=user_id,
                    full_name=user_info['full_name'],
                    join_date=user_info['join_date'],
                    last_activity=user_info['last_activity']
                )

                # Check if this is from callback query or message
                if hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.edit_message_text(
                        welcome_message,
                        parse_mode='Markdown',
                        reply_markup=self._get_user_keyboard()
                    )
                else:
                    await update.message.reply_text(
                        welcome_message,
                        parse_mode='Markdown',
                        reply_markup=self._get_user_keyboard()
                    )
                bot_logger.log_user_action(user_id, username, "USER_LOGIN", "Regular user logged in")
                logger.info(f"User {user_id} logged in successfully")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_command")

    async def _get_user_welcome_info(self, user_data: dict) -> dict:
        """Get formatted user information for welcome message"""
        try:
            # Format full name - handle None values properly
            first_name = user_data.get('first_name') or ''
            last_name = user_data.get('last_name') or ''

            # Remove None values and clean up
            if first_name == 'None':
                first_name = ''
            if last_name == 'None':
                last_name = ''

            full_name = f"{first_name} {last_name}".strip()
            if not full_name:
                full_name = "کاربر عزیز"

            # Format dates
            join_date = "نامشخص"
            last_activity = "نامشخص"

            # Get user info from database
            user_id = user_data.get('user_id')
            if user_id:
                try:
                    # Try to get from JSON database first
                    json_data = db_manager.load_json_data()
                    user_str = str(user_id)
                    if user_str in json_data.get('users', {}):
                        user_info = json_data['users'][user_str]

                        # Format join date
                        if user_info.get('first_join_date'):
                            try:
                                join_dt = datetime.fromisoformat(user_info['first_join_date'].replace('Z', '+00:00'))
                                join_date = join_dt.strftime('%Y/%m/%d %H:%M')
                            except:
                                join_date = "نامشخص"

                        # Format last activity
                        if user_info.get('last_activity'):
                            try:
                                activity_dt = datetime.fromisoformat(user_info['last_activity'].replace('Z', '+00:00'))
                                last_activity = activity_dt.strftime('%Y/%m/%d %H:%M')
                            except:
                                last_activity = "نامشخص"
                except Exception as e:
                    logger.warning(f"Could not get user info from database: {e}")

            return {
                'full_name': full_name,
                'join_date': join_date,
                'last_activity': last_activity
            }

        except Exception as e:
            logger.error(f"Error getting user welcome info: {e}")
            return {
                'full_name': "کاربر عزیز",
                'join_date': "نامشخص",
                'last_activity': "نامشخص"
            }

    async def update_user_keyboard_only(self, update: Update, _: ContextTypes.DEFAULT_TYPE, message: str = None) -> None:
        """Update user keyboard without welcome message"""
        try:
            user_id = update.effective_user.id
            settings = settings_service.load_settings()
            maintenance_mode = settings.get("general", {}).get("maintenance_mode", False)

            if maintenance_mode:
                # In maintenance mode, remove keyboard completely
                await update.message.reply_text(
                    message or "🔧 **ربات در حال تعمیر است**\n\nکیبورد حذف شد.",
                    parse_mode='Markdown',
                    reply_markup=ReplyKeyboardRemove()
                )
            else:
                # Check admin status and update with appropriate keyboard
                is_admin = await self.auth_utils.is_admin(user_id)

                if is_admin:
                    keyboard = self._get_admin_keyboard(user_id)
                    default_message = "🔄 **کیبورد ادمین بروزرسانی شد**"
                else:
                    keyboard = self._get_user_keyboard()
                    default_message = "🔄 **کیبورد کاربر بروزرسانی شد**"

                await update.message.reply_text(
                    message or default_message,
                    parse_mode='Markdown',
                    reply_markup=keyboard
                )

        except Exception as e:
            logger.error(f"Error updating user keyboard: {str(e)}")
            await update.message.reply_text("خطا در بروزرسانی کیبورد")

    async def refresh_user_keyboard(self, context, user_id: int, custom_message: str = None) -> None:
        """Refresh user keyboard based on current admin status (for notifications)"""
        try:
            # Fresh check of admin status from database
            is_admin = await self.auth_utils.is_admin(user_id)

            if is_admin:
                keyboard = self._get_admin_keyboard(user_id)
                message = custom_message or "🔄 کیبورد ادمین بروزرسانی شد"
            else:
                keyboard = self._get_user_keyboard()
                message = custom_message or "🔄 کیبورد کاربر بروزرسانی شد"

            await context.bot.send_message(
                chat_id=user_id,
                text=message,
                reply_markup=keyboard
            )

            logger.info(f"Keyboard refreshed for user {user_id} (admin: {is_admin})")

        except Exception as e:
            logger.error(f"Failed to refresh keyboard for user {user_id}: {str(e)}")
            # Don't raise exception as this is not critical

    async def _check_slash_command_access(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                        button_key: str = None, check_sales: bool = False) -> bool:
        """
        Check if user has access to slash commands
        Returns True if access is allowed, False otherwise
        """
        try:
            user_id = update.effective_user.id
            username = update.effective_user.username or "N/A"

            # Always reload settings to get the latest state
            settings = settings_service.load_settings()
            general_settings = settings.get("general", {})
            user_buttons = settings.get("user_buttons", {})

            # Check maintenance mode first
            if general_settings.get("maintenance_mode", False):
                await update.message.reply_text(
                    "🔧 **ربات در حال تعمیر است**\n\nلطفاً منتظر باشید و بعداً دوباره دستور /start را ارسال کنید.",
                    parse_mode='Markdown',
                    reply_markup=ReplyKeyboardRemove()
                )
                bot_logger.log_user_action(user_id, username, "MAINTENANCE_ACCESS", f"User tried to access during maintenance via slash command")
                return False

            # Check if specific button is enabled
            if button_key and not user_buttons.get(f"{button_key}_enabled", True):
                button_display_names = {
                    "apple_ids": "مشاهده Apple ID ها",
                    "prices": "قیمت ها",
                    "purchase_plans": "پلن‌های خرید",
                    "wallet": "کیف پول",
                    "support": "پشتیبانی",
                    "help": "راهنما"
                }
                display_name = button_display_names.get(button_key, button_key)
                await self.update_user_keyboard_only(
                    update, context,
                    f"❌ **{display_name} فعلاً در دسترس نیست**\n\nکیبورد بروزرسانی شد. لطفاً بعداً امتحان کنید یا دوباره دستور /start را ارسال کنید."
                )
                return False

            # Check sales status for purchase-related functions
            if check_sales and not general_settings.get("sales_enabled", True):
                await self.update_user_keyboard_only(
                    update, context,
                    f"❌ **فروش فعلاً بسته است**\n\nکیبورد بروزرسانی شد. لطفاً بعداً امتحان کنید یا دوباره دستور /start را ارسال کنید."
                )
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking slash command access: {str(e)}")
            return False
    
    def _get_admin_keyboard(self, user_id: int = None):
        """Get admin keyboard markup based on admin type"""
        # Base keyboard for all admins
        keyboard = [
            [KeyboardButton("📊 آمار ربات"), KeyboardButton("📱 مدیریت Apple ID")],
            [KeyboardButton("🗄️ مدیریت دیتابیس"), KeyboardButton("💸 مدیریت قیمت دامنه‌ها")],
            [KeyboardButton("📦 مدیریت پلن‌ها"), KeyboardButton("💰 تراکنش‌های در انتظار")],
            [KeyboardButton("🔍 جست‌وجوی کاربر"), KeyboardButton("🖥️ آمار و اطلاعات سرور")],
            [KeyboardButton("📢 ارسال پیام به همه"), KeyboardButton("📱 نمایش اکانت‌ها از دیتابیس")],
            [KeyboardButton("📢 مدیریت کانال‌های جوین اجباری"), KeyboardButton("⚙️ تنظیمات")]
        ]

        # Add master admin only buttons
        if user_id and AuthUtils().is_master_admin(user_id):
            # Insert master admin buttons in the third row
            keyboard.insert(2, [KeyboardButton("👥 مدیریت کاربران"), KeyboardButton("👑 مدیریت ادمین‌ها")])

        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    def _get_user_keyboard(self):
        """Get user keyboard markup based on settings"""
        try:
            settings = settings_service.load_settings()
            user_buttons = settings.get("user_buttons", {})

            # Default button configurations
            button_configs = [
                ("single_purchase", DEFAULT_BTN_SINGLE_PURCHASE, True),  # Single purchase - always enabled
                ("purchase_plans", settings_service.get_button_text("purchase_plans"), user_buttons.get("purchase_plans_enabled", True)),
                ("apple_ids", settings_service.get_button_text("apple_ids"), user_buttons.get("apple_ids_enabled", True)),
                ("prices", settings_service.get_button_text("prices"), user_buttons.get("prices_enabled", True)),
                ("wallet", settings_service.get_button_text("wallet"), user_buttons.get("wallet_enabled", True)),
                ("support", settings_service.get_button_text("support"), user_buttons.get("support_enabled", True)),
                ("help", settings_service.get_button_text("help"), user_buttons.get("help_enabled", True))
            ]

            # Build keyboard based on enabled buttons
            keyboard = []
            current_row = []

            for _, button_text, enabled in button_configs:
                if enabled:
                    current_row.append(KeyboardButton(button_text))

                    # Add row when it has 2 buttons or it's the last button
                    if len(current_row) == 2:
                        keyboard.append(current_row)
                        current_row = []

            # Add remaining button if any
            if current_row:
                keyboard.append(current_row)

            # If no buttons are enabled, show a minimal keyboard
            if not keyboard:
                keyboard = [[KeyboardButton(settings_service.get_button_text("support"))]]

            return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

        except Exception as e:
            logger.error(f"Error creating user keyboard: {str(e)}")
            # Fallback to default keyboard
            keyboard = [
                [KeyboardButton(DEFAULT_BTN_SINGLE_PURCHASE), KeyboardButton(DEFAULT_BTN_PURCHASE_PLANS)],
                [KeyboardButton(DEFAULT_BTN_APPLE_IDS), KeyboardButton(DEFAULT_BTN_PRICES)],
                [KeyboardButton(DEFAULT_BTN_WALLET), KeyboardButton(DEFAULT_BTN_SUPPORT)],
                [KeyboardButton(DEFAULT_BTN_HELP)]
            ]
            return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

    async def handle_plans_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /plans command"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin - remind them this is for regular users
            if await AuthUtils().is_admin(user_id):
                await update.message.reply_text(
                    "ℹ️ **یادآوری برای ادمین**\n\n"
                    "دستور `/plans` برای کاربران عادی طراحی شده است.\n"
                    "شما به عنوان ادمین می‌توانید از پنل ادمین استفاده کنید.\n\n"
                    "💡 برای مدیریت پلن‌ها از دکمه **📱 مدیریت Apple ID** استفاده کنید.",
                    parse_mode='Markdown'
                )
                bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "PLANS_COMMAND_ADMIN_REMINDER", "Admin used user command")
                return

            # Check access permissions for regular users
            if not await self._check_slash_command_access(update, context, "purchase_plans", check_sales=True):
                return

            await purchase_service.show_purchase_plans(update, context)
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "PLANS_COMMAND", "Plans command used")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "plans_command")

    async def handle_wallet_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /wallet command"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin - remind them this is for regular users
            if await AuthUtils().is_admin(user_id):
                await update.message.reply_text(
                    "ℹ️ **یادآوری برای ادمین**\n\n"
                    "دستور `/wallet` برای کاربران عادی طراحی شده است.\n"
                    "شما به عنوان ادمین می‌توانید از پنل ادمین استفاده کنید.\n\n"
                    "💡 برای مدیریت واریزها از دکمه **💰 واریزهای در انتظار** استفاده کنید.",
                    parse_mode='Markdown'
                )
                bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "WALLET_COMMAND_ADMIN_REMINDER", "Admin used user command")
                return

            # Check access permissions for regular users
            if not await self._check_slash_command_access(update, context, "wallet"):
                return

            await wallet_service.show_wallet_menu(update, context)
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "WALLET_COMMAND", "Wallet command used")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "wallet_command")

    async def handle_support_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /support command"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin - remind them this is for regular users
            if await AuthUtils().is_admin(user_id):
                await update.message.reply_text(
                    "ℹ️ **یادآوری برای ادمین**\n\n"
                    "دستور `/support` برای کاربران عادی طراحی شده است.\n"
                    "شما به عنوان ادمین نیازی به تماس با پشتیبانی ندارید!\n\n"
                    "💡 شما خود پشتیبان هستید! 😊",
                    parse_mode='Markdown'
                )
                bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "SUPPORT_COMMAND_ADMIN_REMINDER", "Admin used user command")
                return

            # Check access permissions for regular users
            if not await self._check_slash_command_access(update, context, "support"):
                return

            await user_service.show_support(update, context)
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "SUPPORT_COMMAND", "Support command used")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "support_command")

    async def handle_help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /help command"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin - remind them this is for regular users
            if await AuthUtils().is_admin(user_id):
                await update.message.reply_text(
                    "ℹ️ **یادآوری برای ادمین**\n\n"
                    "دستور `/help` برای کاربران عادی طراحی شده است.\n"
                    "شما به عنوان ادمین از پنل ادمین استفاده می‌کنید.\n\n"
                    "💡 تمام امکانات ادمین در کیبورد شما موجود است.",
                    parse_mode='Markdown'
                )
                bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "HELP_COMMAND_ADMIN_REMINDER", "Admin used user command")
                return

            # Check access permissions for regular users
            if not await self._check_slash_command_access(update, context, "help"):
                return

            await user_service.show_help(update, context)
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "HELP_COMMAND", "Help command used")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "help_command")

    async def handle_cancel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /cancel command"""
        try:
            user_id = update.effective_user.id

            # Cancel any pending orders
            from services.single_purchase_service import single_purchase_service
            from services.purchase_service import purchase_service

            # Cancel single purchase orders
            await single_purchase_service.cancel_pending_orders(user_id)

            # Cancel plan orders
            await purchase_service.cancel_pending_plan_orders(user_id)

            # Clear any ongoing processes
            context.user_data.clear()

            message = "❌ **عملیات لغو شد**\n\n"
            message += "تمام پردازش‌های جاری متوقف شدند.\n"
            message += "برای شروع مجدد از منوی اصلی استفاده کنید."

            await update.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_user_action(
                update.effective_user.id,
                update.effective_user.username or "N/A",
                "CANCEL_COMMAND",
                "Cancel command used"
            )
        except Exception as e:
            await error_handler.handle_error(update, context, e, "cancel_command")
