import openpyxl
import aiosqlite
import json
import os
import glob
from datetime import datetime
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
import jdatetime
from logger import bot_logger, logger

# Load environment variables
load_dotenv()

class DatabaseManager:
    """Unified database manager for both Excel and SQL databases"""
    
    def __init__(self):
        # Excel database configuration
        self.excel_file = os.getenv('EXCEL_FILE_PATH', 'database/data.xlsx')
        self.workbook = None
        self.worksheet = None

        # SQL database configuration
        self.sql_db_path = os.getenv('SQL_DATABASE_PATH', 'database/bot_database.db')

        # JSON database configuration
        self.json_db_path = os.getenv('JSON_DATABASE_PATH', 'database/bot_data.json')

        # Backup configuration
        self.json_backup_path = os.getenv('JSON_BACKUP_PATH', 'backups/')
        
        # Initialize all databases
        self.ensure_database_directory()
        self.ensure_backup_directory()
        self.initialize_excel_database()
        self.initialize_json_database()
    
    def ensure_database_directory(self):
        """Ensure database directory exists"""
        database_dir = "database"
        if not os.path.exists(database_dir):
            os.makedirs(database_dir)
            logger.info(f"Created database directory: {database_dir}")

    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        if not os.path.exists(self.json_backup_path):
            os.makedirs(self.json_backup_path)
            logger.info(f"Created backup directory: {self.json_backup_path}")

    # ==================== EXCEL DATABASE METHODS ====================
    
    def initialize_excel_database(self):
        """Initialize Excel database connection"""
        try:
            logger.info("Initializing Excel database connection")
            
            if not os.path.exists(self.excel_file):
                logger.warning(f"Excel file {self.excel_file} not found, creating new one")
                self.create_default_excel()
            
            self.workbook = openpyxl.load_workbook(self.excel_file)
            self.worksheet = self.workbook.active
            
            bot_logger.log_database_operation("EXCEL_INIT", f"Connected to {self.excel_file}")
            logger.info("Excel database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Excel database: {str(e)}")
            bot_logger.log_error(e, context="Excel database initialization")
            raise
    
    def create_default_excel(self):
        """Create default Excel file with headers"""
        try:
            logger.debug("Creating default Excel file")

            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Apple_IDs"

            # Set headers to match new structure (removed country and price)
            headers = ['id', 'email', 'pass', 'q1', 'q2', 'q3', 'date', 'status', 'purchase_date', 'time', 'first_name', 'last_name', 'telegram_id', 'numeric_id']
            for col, header in enumerate(headers, 1):
                worksheet.cell(row=1, column=col, value=header)

            workbook.save(self.excel_file)
            logger.info(f"Default Excel file created: {self.excel_file}")

        except Exception as e:
            logger.error(f"Failed to create default Excel file: {str(e)}")
            raise
    
    def get_all_apple_ids(self) -> List[Dict]:
        """Get all Apple IDs from all Excel files"""
        try:
            logger.debug("Fetching all Apple IDs from all Excel files")

            apple_ids = []

            # Get all Excel files in database directory
            database_dir = "database"
            excel_pattern = os.path.join(database_dir, "*.xlsx")
            excel_files = glob.glob(excel_pattern)

            if not excel_files:
                # Fallback to single file if no files in database directory
                if self.excel_file and os.path.exists(self.excel_file):
                    excel_files = [self.excel_file]
                else:
                    logger.warning("No Excel files found")
                    return []

            for file_path in excel_files:
                try:
                    logger.debug(f"Reading Excel file: {file_path}")
                    workbook = openpyxl.load_workbook(file_path)
                    worksheet = workbook.active

                    headers = [cell.value for cell in worksheet[1]]
                    # Normalize headers to lowercase for case-insensitive access
                    normalized_headers = [str(h).lower().strip() if h else '' for h in headers]
                    logger.debug(f"Excel headers: {headers}")
                    logger.debug(f"Normalized headers: {normalized_headers}")

                    for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), 2):
                        if len(row) > 1 and row[1] is not None:  # Check if email exists (column B)
                            apple_id_data = dict(zip(normalized_headers, row))

                            # Log first row for debugging
                            if row_num == 2:
                                logger.debug(f"Sample row data: {apple_id_data}")

                            # Map to expected structure for compatibility (removed Country and Price)
                            mapped_data = {
                                'ID': apple_id_data.get('id', row_num - 1),  # Use ID from column A or row number
                                'Email': apple_id_data.get('email', ''),
                                'Password': apple_id_data.get('pass', ''),
                                'Q1': apple_id_data.get('q1', ''),
                                'Q2': apple_id_data.get('q2', ''),
                                'Q3': apple_id_data.get('q3', ''),
                                'Date': apple_id_data.get('date', ''),
                                'Status': apple_id_data.get('status', ''),
                                'Description': f"Q1: {apple_id_data.get('q1', '')}, Q2: {apple_id_data.get('q2', '')}, Q3: {apple_id_data.get('q3', '')}"
                            }
                            apple_ids.append(mapped_data)

                    workbook.close()

                except Exception as e:
                    logger.warning(f"Error reading Excel file {file_path}: {str(e)}")
                    continue

            bot_logger.log_database_operation("EXCEL_READ_ALL", f"Retrieved {len(apple_ids)} Apple IDs from {len(excel_files)} files")
            logger.debug(f"Successfully retrieved {len(apple_ids)} Apple IDs from {len(excel_files)} Excel files")

            return apple_ids

        except Exception as e:
            logger.error(f"Failed to get Apple IDs from Excel files: {str(e)}")
            bot_logger.log_error(e, context="Get all Apple IDs from Excel files")
            return []
    
    def get_apple_id_by_id(self, apple_id: str) -> Optional[Dict]:
        """Get specific Apple ID by ID or Email from Excel"""
        try:
            logger.debug(f"Fetching Apple ID with ID/Email: {apple_id} from Excel")

            all_apple_ids = self.get_all_apple_ids()
            for item in all_apple_ids:
                # Check both ID and Email for compatibility
                if str(item.get('ID')) == str(apple_id) or str(item.get('Email')) == str(apple_id):
                    bot_logger.log_database_operation("EXCEL_READ_BY_ID", f"Found Apple ID: {apple_id}")
                    return item

            logger.debug(f"Apple ID not found in Excel: {apple_id}")
            return None

        except Exception as e:
            logger.error(f"Failed to get Apple ID by ID from Excel: {str(e)}")
            bot_logger.log_error(e, context=f"Get Apple ID by ID from Excel: {apple_id}")
            return None
    
    def get_available_apple_ids(self) -> List[Dict]:
        """Get only available Apple IDs from Excel (status is empty/None)"""
        try:
            logger.debug("Fetching available Apple IDs from Excel")

            all_apple_ids = self.get_all_apple_ids()
            # Available means status is empty, None, or 'available' - exclude sold statuses
            sold_statuses = ['s', 'sold', 'فروخته شده', 'فروخته']
            available_ids = []

            for item in all_apple_ids:
                status_raw = item.get('Status')
                if status_raw is None:
                    status = ''
                else:
                    status = str(status_raw).strip().lower()

                if not status or status == 'available' or status not in sold_statuses:
                    available_ids.append(item)

            bot_logger.log_database_operation("EXCEL_READ_AVAILABLE", f"Found {len(available_ids)} available Apple IDs out of {len(all_apple_ids)} total")
            logger.debug(f"Found {len(available_ids)} available Apple IDs out of {len(all_apple_ids)} total in Excel")

            return available_ids

        except Exception as e:
            logger.error(f"Failed to get available Apple IDs from Excel: {str(e)}")
            bot_logger.log_error(e, context="Get available Apple IDs from Excel")
            return []
    
    def update_apple_id_status(self, apple_id: str, new_status: str, buyer_info: dict = None) -> bool:
        """Update Apple ID status in all Excel files with optional buyer information"""
        try:
            logger.debug(f"Updating Apple ID {apple_id} status to {new_status} in all Excel files")

            # Get all Excel files in database directory
            database_dir = "database"
            excel_pattern = os.path.join(database_dir, "*.xlsx")
            excel_files = glob.glob(excel_pattern)

            if not excel_files:
                # Fallback to single file if no files in database directory
                if self.excel_file and os.path.exists(self.excel_file):
                    excel_files = [self.excel_file]
                else:
                    logger.warning("No Excel files found for status update")
                    return False

            for file_path in excel_files:
                try:
                    workbook = openpyxl.load_workbook(file_path)
                    worksheet = workbook.active

                    headers = [cell.value for cell in worksheet[1]]

                    # Find columns for updating
                    status_col = None
                    purchase_date_col = None
                    time_col = None
                    first_name_col = None
                    last_name_col = None
                    telegram_id_col = None
                    numeric_id_col = None

                    for col_idx, header in enumerate(headers, 1):
                        if header:
                            header_lower = str(header).lower()
                            if header_lower in ['status', 'وضعیت']:
                                status_col = col_idx
                            elif header_lower == 'purchase_date':
                                purchase_date_col = col_idx
                            elif header_lower == 'time':
                                time_col = col_idx
                            elif header_lower == 'first_name':
                                first_name_col = col_idx
                            elif header_lower == 'last_name':
                                last_name_col = col_idx
                            elif header_lower == 'telegram_id':
                                telegram_id_col = col_idx
                            elif header_lower == 'numeric_id':
                                numeric_id_col = col_idx

                    if not status_col:
                        logger.warning(f"Status column not found in Excel file {file_path}")
                        workbook.close()
                        continue

                    # Find by ID (first column) or email (second column)
                    found = False
                    for row_num, row in enumerate(worksheet.iter_rows(min_row=2), 2):
                        # Check both ID column (A) and email column (B)
                        if (str(row[0].value) == str(apple_id) or
                            (len(row) > 1 and str(row[1].value) == str(apple_id))):

                            # Update status
                            worksheet.cell(row=row_num, column=status_col, value=new_status)

                            # Update buyer information if provided
                            if buyer_info and new_status.lower() in ['sold', 'فروخته شده', 's']:
                                # Persian date and time
                                now = datetime.now()
                                persian_date = jdatetime.datetime.fromgregorian(datetime=now).strftime('%Y/%m/%d')
                                iranian_time = now.strftime('%H:%M:%S')

                                if purchase_date_col:
                                    worksheet.cell(row=row_num, column=purchase_date_col, value=persian_date)
                                if time_col:
                                    worksheet.cell(row=row_num, column=time_col, value=iranian_time)
                                if first_name_col and buyer_info.get('first_name'):
                                    worksheet.cell(row=row_num, column=first_name_col, value=buyer_info['first_name'])
                                if last_name_col and buyer_info.get('last_name'):
                                    worksheet.cell(row=row_num, column=last_name_col, value=buyer_info['last_name'])
                                if telegram_id_col and buyer_info.get('username'):
                                    # Remove @ from username if present
                                    username = buyer_info['username'].replace('@', '') if buyer_info['username'] else ''
                                    worksheet.cell(row=row_num, column=telegram_id_col, value=username)
                                if numeric_id_col and buyer_info.get('user_id'):
                                    worksheet.cell(row=row_num, column=numeric_id_col, value=buyer_info['user_id'])

                                # Apply light red background color to the entire row (columns A to N)
                                from openpyxl.styles import PatternFill
                                light_red_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")

                                for col in range(1, 15):  # Columns A to N (1 to 14)
                                    cell = worksheet.cell(row=row_num, column=col)
                                    cell.fill = light_red_fill

                            workbook.save(file_path)
                            workbook.close()

                            bot_logger.log_database_operation("EXCEL_UPDATE_STATUS", f"Apple ID {apple_id} status updated to {new_status} in {file_path}")
                            logger.info(f"Apple ID {apple_id} status updated to {new_status} in {file_path}")
                            return True

                    workbook.close()

                except Exception as e:
                    logger.warning(f"Error updating status in file {file_path}: {str(e)}")
                    continue

            logger.warning(f"Apple ID {apple_id} not found in any Excel file for status update")
            bot_logger.log_database_operation("EXCEL_UPDATE_STATUS_NOT_FOUND", f"Apple ID {apple_id} not found for status update")
            return False

        except Exception as e:
            logger.error(f"Failed to update Apple ID status in Excel files: {str(e)}")
            bot_logger.log_error(e, context=f"Update status for Apple ID in Excel files: {apple_id}")
            return False

    async def mark_apple_id_as_sold(self, apple_id_email: str, buyer_info: dict = None) -> bool:
        """Mark Apple ID as sold in Excel files"""
        try:
            # Use 'S' as sold status character
            sold_status = 'S'

            # Update status in Excel files
            success = self.update_apple_id_status(apple_id_email, sold_status, buyer_info)

            if success:
                logger.info(f"Apple ID {apple_id_email} marked as sold")
                bot_logger.log_database_operation("MARK_AS_SOLD", f"Apple ID {apple_id_email} marked as sold")
                return True
            else:
                logger.warning(f"Failed to mark Apple ID {apple_id_email} as sold")
                return False

        except Exception as e:
            logger.error(f"Error marking Apple ID as sold: {str(e)}")
            bot_logger.log_error(e, context=f"Mark Apple ID as sold: {apple_id_email}")
            return False

    def mark_apple_id_as_available(self, apple_id_email: str) -> bool:
        """Mark Apple ID as available again (clear buyer information)"""
        try:
            # Clear status (make it available again)
            success = self.update_apple_id_status(apple_id_email, '', None)

            if success:
                logger.info(f"Apple ID {apple_id_email} marked as available")
                bot_logger.log_database_operation("MARK_AS_AVAILABLE", f"Apple ID {apple_id_email} marked as available")
                return True
            else:
                logger.warning(f"Failed to mark Apple ID {apple_id_email} as available")
                return False

        except Exception as e:
            logger.error(f"Error marking Apple ID as available: {str(e)}")
            bot_logger.log_error(e, context=f"Mark Apple ID as available: {apple_id_email}")
            return False

    def get_apple_id_by_id(self, apple_id: str) -> Dict:
        """Get Apple ID details by ID from Excel"""
        try:
            logger.debug(f"Getting Apple ID {apple_id} from Excel")

            if not self.worksheet:
                self.initialize_excel_database()

            headers = [cell.value for cell in self.worksheet[1]]

            for _, row in enumerate(self.worksheet.iter_rows(min_row=2, values_only=True), 2):
                if len(row) > 0 and str(row[0]) == str(apple_id):  # Check ID column (A)
                    apple_id_data = dict(zip(headers, row))

                    # Map to expected structure (removed Country and Price)
                    mapped_data = {
                        'ID': apple_id_data.get('id', ''),
                        'Email': apple_id_data.get('email', ''),
                        'Password': apple_id_data.get('pass', ''),
                        'Q1': apple_id_data.get('q1', ''),
                        'Q2': apple_id_data.get('q2', ''),
                        'Q3': apple_id_data.get('q3', ''),
                        'Date': apple_id_data.get('date', ''),
                        'Status': apple_id_data.get('status', '')
                    }

                    bot_logger.log_database_operation("EXCEL_READ_BY_ID", f"Retrieved Apple ID {apple_id}")
                    return mapped_data

            logger.warning(f"Apple ID {apple_id} not found in Excel")
            return None

        except Exception as e:
            logger.error(f"Failed to get Apple ID by ID from Excel: {str(e)}")
            bot_logger.log_error(e, context=f"Get Apple ID by ID from Excel: {apple_id}")
            return None

    def close_excel_connection(self):
        """Close Excel database connection"""
        try:
            if self.workbook:
                self.workbook.close()
                logger.info("Excel database connection closed")
        except Exception as e:
            logger.error(f"Error closing Excel database connection: {str(e)}")
    
    # ==================== JSON DATABASE METHODS ====================

    def initialize_json_database(self):
        """Initialize JSON database"""
        try:
            logger.info("Initializing JSON database")

            # Create default structure if file doesn't exist
            if not os.path.exists(self.json_db_path):
                default_data = {
                    "users": {},
                    "purchase_plans": {},
                    "purchases": {},
                    "wallet_transactions": {},
                    "forced_join_channels": {},
                    "metadata": {
                        "created_at": datetime.now().isoformat(),
                        "last_updated": datetime.now().isoformat(),
                        "version": "1.0"
                    }
                }
                self.save_json_data(default_data)
                logger.info(f"Created new JSON database: {self.json_db_path}")
            else:
                logger.info(f"JSON database already exists: {self.json_db_path}")

        except Exception as e:
            logger.error(f"Failed to initialize JSON database: {str(e)}")
            raise

    def load_json_data(self) -> Dict[str, Any]:
        """Load data from JSON database"""
        try:
            if os.path.exists(self.json_db_path):
                with open(self.json_db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "users": {},
                    "purchase_plans": {},
                    "purchases": {},
                    "wallet_transactions": {},
                    "metadata": {
                        "created_at": datetime.now().isoformat(),
                        "last_updated": datetime.now().isoformat(),
                        "version": "1.0"
                    }
                }
        except Exception as e:
            logger.error(f"Failed to load JSON data: {str(e)}")
            return {}

    def save_json_data(self, data: Dict[str, Any]) -> bool:
        """Save data to JSON database"""
        try:
            # Update metadata
            if "metadata" not in data:
                data["metadata"] = {}
            data["metadata"]["last_updated"] = datetime.now().isoformat()

            # Save to file with backup
            temp_path = f"{self.json_db_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # Atomic replace
            if os.path.exists(self.json_db_path):
                backup_path = f"{self.json_db_path}.backup"
                os.replace(self.json_db_path, backup_path)

            os.replace(temp_path, self.json_db_path)

            # Remove backup if successful
            backup_path = f"{self.json_db_path}.backup"
            if os.path.exists(backup_path):
                os.remove(backup_path)

            return True

        except Exception as e:
            logger.error(f"Failed to save JSON data: {str(e)}")
            # Restore backup if exists
            backup_path = f"{self.json_db_path}.backup"
            if os.path.exists(backup_path):
                os.replace(backup_path, self.json_db_path)
            return False

    def sync_sql_to_json(self) -> bool:
        """Sync all SQL data to JSON"""
        try:
            logger.info("Syncing SQL data to JSON")

            json_data = self.load_json_data()

            # Sync users
            async def sync_users():
                async with aiosqlite.connect(self.sql_db_path) as db:
                    cursor = await db.execute("SELECT * FROM users")
                    users = await cursor.fetchall()

                    json_data["users"] = {}
                    for user in users:
                        user_dict = {
                            "user_id": user[0],
                            "username": user[1],
                            "first_name": user[2],
                            "last_name": user[3],
                            "phone_number": user[4],
                            "is_admin": bool(user[5]),
                            "wallet_balance": user[6],
                            "total_purchases": user[7],
                            "total_spent": user[8],
                            "created_at": user[9],
                            "last_activity": user[10]
                        }
                        json_data["users"][str(user[0])] = user_dict

            # Sync purchase plans
            async def sync_plans():
                async with aiosqlite.connect(self.sql_db_path) as db:
                    cursor = await db.execute("SELECT * FROM purchase_plans")
                    plans = await cursor.fetchall()

                    json_data["purchase_plans"] = {}
                    for plan in plans:
                        plan_dict = {
                            "id": plan[0],
                            "name": plan[1],
                            "quantity": plan[2],
                            "price": plan[3],
                            "domain": plan[4] if len(plan) > 4 else '',
                            "has_warranty": bool(plan[5] if len(plan) > 5 else plan[4]),
                            "warranty_days": plan[6] if len(plan) > 6 else plan[5],
                            "description": plan[7] if len(plan) > 7 else plan[6],
                            "is_active": bool(plan[8] if len(plan) > 8 else plan[7]),
                            "created_at": plan[9] if len(plan) > 9 else plan[8]
                        }
                        json_data["purchase_plans"][str(plan[0])] = plan_dict

            # Sync purchases
            async def sync_purchases():
                async with aiosqlite.connect(self.sql_db_path) as db:
                    cursor = await db.execute("SELECT * FROM purchases")
                    purchases = await cursor.fetchall()

                    json_data["purchases"] = {}
                    for purchase in purchases:
                        purchase_dict = {
                            "id": purchase[0],
                            "user_id": purchase[1],
                            "plan_id": purchase[2],
                            "apple_ids": purchase[3],
                            "total_amount": purchase[4],
                            "purchase_date": purchase[5]
                        }
                        json_data["purchases"][str(purchase[0])] = purchase_dict

            # Sync wallet transactions
            async def sync_transactions():
                async with aiosqlite.connect(self.sql_db_path) as db:
                    cursor = await db.execute("SELECT * FROM wallet_transactions")
                    transactions = await cursor.fetchall()

                    json_data["wallet_transactions"] = {}
                    for transaction in transactions:
                        transaction_dict = {
                            "id": transaction[0],
                            "user_id": transaction[1],
                            "amount": transaction[2],
                            "transaction_type": transaction[3],
                            "status": transaction[4],
                            "description": transaction[5],
                            "created_at": transaction[6]
                        }
                        json_data["wallet_transactions"][str(transaction[0])] = transaction_dict

            # Run all sync operations
            try:
                import asyncio
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            try:
                loop.run_until_complete(sync_users())
                loop.run_until_complete(sync_plans())
                loop.run_until_complete(sync_purchases())
                loop.run_until_complete(sync_transactions())
            finally:
                if loop.is_running():
                    pass  # Don't close running loop
                else:
                    loop.close()

            # Save updated JSON data
            return self.save_json_data(json_data)

        except Exception as e:
            logger.error(f"Failed to sync SQL to JSON: {str(e)}")
            return False

    async def save_user_to_json(self, user_data: Dict, is_update: bool = False) -> bool:
        """Save user data to JSON database"""
        try:
            json_data = self.load_json_data()

            current_time = datetime.now().isoformat()
            user_id_str = str(user_data['user_id'])

            if is_update and user_id_str in json_data["users"]:
                # Update existing user
                existing_user = json_data["users"][user_id_str]
                existing_user.update({
                    "username": user_data.get('username'),
                    "first_name": user_data.get('first_name'),
                    "last_name": user_data.get('last_name'),
                    "language_code": user_data.get('language_code'),
                    "is_bot": user_data.get('is_bot', 0),
                    "is_premium": user_data.get('is_premium', 0),
                    "last_activity": current_time,
                    "updated_at": current_time
                })
            else:
                # Create new user
                json_data["users"][user_id_str] = {
                    "user_id": user_data['user_id'],
                    "username": user_data.get('username'),
                    "first_name": user_data.get('first_name'),
                    "last_name": user_data.get('last_name'),
                    "language_code": user_data.get('language_code'),
                    "is_bot": user_data.get('is_bot', 0),
                    "is_premium": user_data.get('is_premium', 0),
                    "wallet_balance": 0,
                    "total_purchases": 0,
                    "total_spent": 0,
                    "first_join_date": current_time,
                    "last_activity": current_time,
                    "created_at": current_time,
                    "updated_at": current_time
                }

            return self.save_json_data(json_data)

        except Exception as e:
            logger.error(f"Failed to save user to JSON: {str(e)}")
            return False

    async def save_purchase_plan_to_json(self, plan_data: Dict, plan_id: int) -> bool:
        """Save purchase plan to JSON database"""
        try:
            json_data = self.load_json_data()

            current_time = datetime.now().isoformat()
            plan_id_str = str(plan_id)

            json_data["purchase_plans"][plan_id_str] = {
                "id": plan_id,
                "name": plan_data['name'],
                "quantity": plan_data['quantity'],
                "price": plan_data['price'],
                "domain": plan_data.get('domain', ''),
                "has_warranty": plan_data.get('has_warranty', 0),
                "warranty_days": plan_data.get('warranty_days', 0),
                "description": plan_data.get('description', ''),
                "is_active": plan_data.get('is_active', 1),
                "created_at": current_time
            }

            return self.save_json_data(json_data)

        except Exception as e:
            logger.error(f"Failed to save purchase plan to JSON: {str(e)}")
            return False

    async def save_purchase_to_json(self, purchase_data: Dict, purchase_id: int) -> bool:
        """Save purchase to JSON database"""
        try:
            json_data = self.load_json_data()

            current_time = datetime.now().isoformat()
            purchase_id_str = str(purchase_id)

            json_data["purchases"][purchase_id_str] = {
                "id": purchase_id,
                "user_id": purchase_data['user_id'],
                "plan_id": purchase_data['plan_id'],
                "apple_ids": purchase_data['apple_ids'],
                "total_amount": purchase_data['total_amount'],
                "purchase_date": current_time
            }

            return self.save_json_data(json_data)

        except Exception as e:
            logger.error(f"Failed to save purchase to JSON: {str(e)}")
            return False

    async def save_wallet_transaction_to_json(self, transaction_data: Dict, transaction_id: int) -> bool:
        """Save wallet transaction to JSON database"""
        try:
            json_data = self.load_json_data()

            current_time = datetime.now().isoformat()
            transaction_id_str = str(transaction_id)

            json_data["wallet_transactions"][transaction_id_str] = {
                "id": transaction_id,
                "user_id": transaction_data['user_id'],
                "amount": transaction_data['amount'],
                "transaction_type": transaction_data['transaction_type'],
                "status": transaction_data['status'],
                "description": transaction_data['description'],
                "created_at": current_time
            }

            return self.save_json_data(json_data)

        except Exception as e:
            logger.error(f"Failed to save wallet transaction to JSON: {str(e)}")
            return False

    async def update_user_wallet_in_json(self, user_id: int, new_balance: float) -> bool:
        """Update user wallet balance in JSON database"""
        try:
            json_data = self.load_json_data()
            user_id_str = str(user_id)

            if user_id_str in json_data["users"]:
                json_data["users"][user_id_str]["wallet_balance"] = new_balance
                json_data["users"][user_id_str]["updated_at"] = datetime.now().isoformat()
                return self.save_json_data(json_data)

            return False

        except Exception as e:
            logger.error(f"Failed to update user wallet in JSON: {str(e)}")
            return False

    # ==================== SQL DATABASE METHODS ====================
    
    async def initialize_sql_database(self):
        """Initialize SQL database with all required tables"""
        try:
            logger.info("Initializing SQL database...")
            
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Users table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        user_id INTEGER PRIMARY KEY,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        language_code TEXT,
                        is_bot INTEGER DEFAULT 0,
                        is_premium INTEGER DEFAULT 0,
                        wallet_balance INTEGER DEFAULT 0,
                        first_join_date TEXT,
                        last_activity TEXT,
                        total_purchases INTEGER DEFAULT 0,
                        total_spent INTEGER DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Purchase plans table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS purchase_plans (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        price INTEGER NOT NULL,
                        domain TEXT,
                        has_warranty INTEGER DEFAULT 0,
                        warranty_days INTEGER DEFAULT 0,
                        description TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Purchases table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS purchases (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        plan_id INTEGER,
                        apple_ids TEXT,
                        total_price INTEGER,
                        purchase_date TEXT,
                        status TEXT DEFAULT 'completed',
                        warranty_until TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id),
                        FOREIGN KEY (plan_id) REFERENCES purchase_plans (id)
                    )
                ''')
                
                # Wallet transactions table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS wallet_transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        transaction_type TEXT,
                        amount INTEGER,
                        status TEXT DEFAULT 'pending',
                        receipt_message_id INTEGER,
                        admin_user_id INTEGER,
                        description TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        processed_at TEXT,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')

                # Single orders table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS single_orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_id TEXT UNIQUE NOT NULL,
                        user_id INTEGER NOT NULL,
                        username TEXT,
                        first_name TEXT,
                        domain TEXT NOT NULL,
                        price INTEGER NOT NULL,
                        status TEXT DEFAULT 'pending',
                        order_date TEXT,
                        receipt_date TEXT,
                        payment_receipt TEXT,
                        receipt_message_id INTEGER,
                        admin_user_id INTEGER,
                        apple_id_assigned TEXT,
                        apple_id_sent TEXT,
                        admin_notes TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')

                # Plan orders table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS plan_orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_id TEXT UNIQUE NOT NULL,
                        user_id INTEGER NOT NULL,
                        plan_id INTEGER NOT NULL,
                        plan_name TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        price INTEGER NOT NULL,
                        domain TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        receipt_message_id INTEGER,
                        admin_user_id INTEGER,
                        apple_ids_assigned TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id),
                        FOREIGN KEY (plan_id) REFERENCES purchase_plans (id)
                    )
                ''')

                # Admins table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS admins (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER UNIQUE NOT NULL,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        added_by INTEGER NOT NULL,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (added_by) REFERENCES users (user_id)
                    )
                ''')

                # Domain prices table for email domain-based pricing
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS domain_prices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        domain TEXT UNIQUE NOT NULL,
                        price INTEGER NOT NULL,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Forced join channels table
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS forced_join_channels (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT UNIQUE NOT NULL,
                        channel_title TEXT,
                        channel_username TEXT,
                        is_active INTEGER DEFAULT 1,
                        added_by INTEGER NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (added_by) REFERENCES users (user_id)
                    )
                ''')

                await db.commit()

                # Run database migrations
                await self.run_database_migrations()

                # Initialize default domain prices if table is empty
                await self.initialize_default_domain_prices()

                logger.info("SQL database initialized successfully")
                bot_logger.log_database_operation("SQL_INIT", "Database tables created/verified")
                
        except Exception as e:
            logger.error(f"Failed to initialize SQL database: {str(e)}")
            bot_logger.log_error(e, context="SQL database initialization")
            raise

    async def run_database_migrations(self):
        """Run database migrations to update existing tables"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Check if single_orders table needs migration
                cursor = await db.execute("PRAGMA table_info(single_orders)")
                columns = await cursor.fetchall()
                column_names = [col[1] for col in columns]

                # Add missing columns to single_orders table
                missing_columns = []
                expected_columns = {
                    'username': 'TEXT',
                    'first_name': 'TEXT',
                    'order_date': 'TEXT',
                    'receipt_date': 'TEXT',
                    'payment_receipt': 'TEXT',
                    'apple_id_sent': 'TEXT',
                    'admin_notes': 'TEXT'
                }

                for col_name, col_type in expected_columns.items():
                    if col_name not in column_names:
                        missing_columns.append((col_name, col_type))

                # Add missing columns
                for col_name, col_type in missing_columns:
                    await db.execute(f"ALTER TABLE single_orders ADD COLUMN {col_name} {col_type}")
                    logger.info(f"Added column {col_name} to single_orders table")

                await db.commit()

                if missing_columns:
                    logger.info(f"Database migration completed: Added {len(missing_columns)} columns to single_orders")
                else:
                    logger.debug("No database migration needed for single_orders table")

                # Check if plan_orders table needs migration
                cursor = await db.execute("PRAGMA table_info(plan_orders)")
                columns = await cursor.fetchall()
                column_names = [col[1] for col in columns]

                # Add missing columns to plan_orders table
                missing_plan_columns = []
                expected_plan_columns = {
                    'username': 'TEXT',
                    'first_name': 'TEXT',
                    'last_name': 'TEXT',
                    'updated_at': 'TEXT'
                }

                for col_name, col_type in expected_plan_columns.items():
                    if col_name not in column_names:
                        missing_plan_columns.append((col_name, col_type))

                # Add missing columns
                for col_name, col_type in missing_plan_columns:
                    await db.execute(f"ALTER TABLE plan_orders ADD COLUMN {col_name} {col_type}")
                    logger.info(f"Added column {col_name} to plan_orders table")

                if missing_plan_columns:
                    logger.info(f"Database migration completed: Added {len(missing_plan_columns)} columns to plan_orders")
                else:
                    logger.debug("No database migration needed for plan_orders table")

                # Check if users table needs migration
                cursor = await db.execute("PRAGMA table_info(users)")
                columns = await cursor.fetchall()
                column_names = [col[1] for col in columns]

                # Add missing columns to users table
                missing_user_columns = []
                expected_user_columns = {
                    'is_banned': 'INTEGER DEFAULT 0'
                }

                for col_name, col_type in expected_user_columns.items():
                    if col_name not in column_names:
                        missing_user_columns.append((col_name, col_type))

                # Add missing columns
                for col_name, col_type in missing_user_columns:
                    await db.execute(f"ALTER TABLE users ADD COLUMN {col_name} {col_type}")
                    logger.info(f"Added column {col_name} to users table")

                if missing_user_columns:
                    logger.info(f"Database migration completed: Added {len(missing_user_columns)} columns to users")
                else:
                    logger.debug("No database migration needed for users table")

        except Exception as e:
            logger.error(f"Error running database migrations: {str(e)}")
            # Don't raise here as this shouldn't stop the bot from starting

    # Domain price management methods
    async def get_domain_price(self, domain: str) -> Optional[int]:
        """Get price for a specific domain"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT price FROM domain_prices WHERE domain = ? AND is_active = 1",
                    (domain.lower(),)
                )
                result = await cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get domain price for {domain}: {str(e)}")
            return None

    async def set_domain_price(self, domain: str, price: int) -> bool:
        """Set or update price for a domain"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Check if domain already exists
                cursor = await db.execute(
                    "SELECT id FROM domain_prices WHERE domain = ?",
                    (domain.lower(),)
                )
                existing = await cursor.fetchone()

                current_time = datetime.now().isoformat()

                if existing:
                    # Update existing domain price
                    await db.execute(
                        "UPDATE domain_prices SET price = ?, is_active = 1, updated_at = ? WHERE domain = ?",
                        (price, current_time, domain.lower())
                    )
                else:
                    # Insert new domain price
                    await db.execute(
                        "INSERT INTO domain_prices (domain, price, created_at, updated_at) VALUES (?, ?, ?, ?)",
                        (domain.lower(), price, current_time, current_time)
                    )

                await db.commit()
                logger.info(f"Set price for domain {domain}: {price:,} تومان")
                return True

        except Exception as e:
            logger.error(f"Failed to set domain price for {domain}: {str(e)}")
            return False

    async def get_all_domain_prices(self) -> List[Dict]:
        """Get all domain prices"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT domain, price, is_active, created_at, updated_at FROM domain_prices ORDER BY domain"
                )
                results = await cursor.fetchall()

                return [
                    {
                        'domain': row[0],
                        'price': row[1],
                        'is_active': row[2],
                        'created_at': row[3],
                        'updated_at': row[4]
                    }
                    for row in results
                ]
        except Exception as e:
            logger.error(f"Failed to get all domain prices: {str(e)}")
            return []

    async def delete_domain_price(self, domain: str) -> bool:
        """Delete (deactivate) a domain price"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                await db.execute(
                    "UPDATE domain_prices SET is_active = 0, updated_at = ? WHERE domain = ?",
                    (datetime.now().isoformat(), domain.lower())
                )
                await db.commit()
                logger.info(f"Deactivated domain price for {domain}")
                return True

        except Exception as e:
            logger.error(f"Failed to delete domain price for {domain}: {str(e)}")
            return False

    def extract_domain_from_email(self, email: str) -> str:
        """Extract domain from email address with smart cleaning"""
        try:
            if not email or '@' not in str(email):
                return 'unknown'

            # Clean the email: remove extra spaces, convert to lowercase
            cleaned_email = str(email).strip().lower()

            # Extract domain part
            domain = cleaned_email.split('@')[1]

            # Clean domain: remove spaces and common typos
            domain = domain.strip()

            # Handle common domain variations and typos
            domain_mappings = {
                'gmail.com ': 'gmail.com',
                ' gmail.com': 'gmail.com',
                'hotmail.com ': 'hotmail.com',
                ' hotmail.com': 'hotmail.com',
                'outlook.com ': 'outlook.com',
                ' outlook.com': 'outlook.com',
                'yahoo.com ': 'yahoo.com',
                ' yahoo.com': 'yahoo.com',
                'icloud.com ': 'icloud.com',
                ' icloud.com': 'icloud.com',
                'live.com ': 'live.com',
                ' live.com': 'live.com',
                'msn.com ': 'msn.com',
                ' msn.com': 'msn.com',
                'aol.com ': 'aol.com',
                ' aol.com': 'aol.com'
            }

            # Apply domain mapping if exists
            if domain in domain_mappings:
                domain = domain_mappings[domain]

            # Final validation: ensure domain has at least one dot and no spaces
            if '.' not in domain or ' ' in domain:
                logger.warning(f"Invalid domain extracted from email {email}: '{domain}'")
                return 'unknown'

            return domain

        except Exception as e:
            logger.error(f"Failed to extract domain from email {email}: {str(e)}")
            return 'unknown'

    async def initialize_default_domain_prices(self) -> None:
        """Initialize default domain prices if table is empty - now does nothing as domains are synced from Excel"""
        try:
            # No longer initialize default prices - domains will be synced from Excel files
            logger.info("Domain prices will be synced from Excel files automatically")

        except Exception as e:
            logger.error(f"Failed to initialize default domain prices: {str(e)}")

    async def create_user(self, user_data: Dict) -> bool:
        """Create or update user in SQL database"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                # Check if user exists
                cursor = await db.execute("SELECT user_id FROM users WHERE user_id = ?", (user_data['user_id'],))
                existing_user = await cursor.fetchone()

                current_time = datetime.now().isoformat()

                if existing_user:
                    # Update existing user
                    await db.execute('''
                        UPDATE users SET
                            username = ?, first_name = ?, last_name = ?,
                            language_code = ?, is_bot = ?, is_premium = ?,
                            last_activity = ?, updated_at = ?
                        WHERE user_id = ?
                    ''', (
                        user_data.get('username'),
                        user_data.get('first_name'),
                        user_data.get('last_name'),
                        user_data.get('language_code'),
                        user_data.get('is_bot', 0),
                        user_data.get('is_premium', 0),
                        current_time,
                        current_time,
                        user_data['user_id']
                    ))
                    logger.debug(f"Updated user {user_data['user_id']} in SQL database")
                else:
                    # Create new user
                    await db.execute('''
                        INSERT INTO users (
                            user_id, username, first_name, last_name,
                            language_code, is_bot, is_premium, wallet_balance,
                            first_join_date, last_activity, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?, ?, ?)
                    ''', (
                        user_data['user_id'],
                        user_data.get('username'),
                        user_data.get('first_name'),
                        user_data.get('last_name'),
                        user_data.get('language_code'),
                        user_data.get('is_bot', 0),
                        user_data.get('is_premium', 0),
                        current_time,
                        current_time,
                        current_time,
                        current_time
                    ))
                    logger.info(f"Created new user {user_data['user_id']} in SQL database")

                await db.commit()

                # Also save to JSON database
                json_success = await self.save_user_to_json(user_data, existing_user is not None)
                if not json_success:
                    logger.warning(f"Failed to save user {user_data['user_id']} to JSON database")

                bot_logger.log_database_operation("DUAL_USER_CREATE_UPDATE", f"User {user_data['user_id']} processed")
                return True

        except Exception as e:
            logger.error(f"Failed to create/update user in SQL: {str(e)}")
            bot_logger.log_error(e, user_id=user_data.get('user_id'), context="Create/update user in SQL")
            return False

    async def get_user(self, user_id: int) -> Optional[Dict]:
        """Get user by ID from SQL database"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
                row = await cursor.fetchone()

                if row:
                    return dict(row)
                return None

        except Exception as e:
            logger.error(f"Failed to get user {user_id} from SQL: {str(e)}")
            bot_logger.log_error(e, user_id=user_id, context="Get user from SQL")
            return None

    async def update_user_wallet(self, user_id: int, amount: int) -> bool:
        """Update user wallet balance in SQL database"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                await db.execute(
                    "UPDATE users SET wallet_balance = wallet_balance + ?, updated_at = ? WHERE user_id = ?",
                    (amount, datetime.now().isoformat(), user_id)
                )
                await db.commit()

                # Get new balance and update JSON
                cursor = await db.execute("SELECT wallet_balance FROM users WHERE user_id = ?", (user_id,))
                result = await cursor.fetchone()
                if result:
                    new_balance = result[0]
                    json_success = await self.update_user_wallet_in_json(user_id, new_balance)
                    if not json_success:
                        logger.warning(f"Failed to update user {user_id} wallet in JSON database")

                bot_logger.log_database_operation("DUAL_WALLET_UPDATE", f"User {user_id} wallet updated by {amount}")
                return True

        except Exception as e:
            logger.error(f"Failed to update wallet for user {user_id} in SQL: {str(e)}")
            bot_logger.log_error(e, user_id=user_id, context="Update wallet in SQL")
            return False

    async def backup_to_json(self) -> str:
        """Create JSON backup of all SQL data"""
        try:
            backup_data = {}
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.json"
            backup_filepath = os.path.join(self.json_backup_path, backup_filename)

            async with aiosqlite.connect(self.sql_db_path) as db:
                db.row_factory = aiosqlite.Row

                # Backup all tables
                tables = ['users', 'purchase_plans', 'purchases', 'wallet_transactions', 'forced_join_channels']

                for table in tables:
                    cursor = await db.execute(f"SELECT * FROM {table}")
                    rows = await cursor.fetchall()
                    backup_data[table] = [dict(row) for row in rows]

            # Write to JSON file
            with open(backup_filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"JSON backup created: {backup_filepath}")
            bot_logger.log_database_operation("JSON_BACKUP", f"Backup created: {backup_filename}")

            return backup_filepath

        except Exception as e:
            logger.error(f"Failed to create JSON backup: {str(e)}")
            bot_logger.log_error(e, context="JSON backup")
            raise

    async def full_sync_sql_to_json(self) -> bool:
        """Perform full synchronization from SQL to JSON"""
        try:
            logger.info("Starting full SQL to JSON synchronization")

            # Initialize SQL database if not exists
            await self.initialize_sql_database()

            # Sync all data
            success = self.sync_sql_to_json()

            if success:
                logger.info("Full SQL to JSON synchronization completed successfully")
                bot_logger.log_database_operation("FULL_SYNC", "SQL to JSON sync completed")
            else:
                logger.error("Full SQL to JSON synchronization failed")

            return success

        except Exception as e:
            logger.error(f"Failed to perform full sync: {str(e)}")
            bot_logger.log_error(e, context="Full SQL to JSON sync")
            return False

    # ==================== FORCED JOIN CHANNELS METHODS ====================

    async def add_forced_join_channel(self, channel_id: str, channel_title: str = None,
                                    channel_username: str = None, added_by: int = None) -> bool:
        """Add a new forced join channel"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO forced_join_channels
                    (channel_id, channel_title, channel_username, added_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    channel_id,
                    channel_title,
                    channel_username,
                    added_by,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                await db.commit()

            logger.info(f"Added forced join channel: {channel_id}")
            bot_logger.log_database_operation("ADD_FORCED_CHANNEL", f"Channel {channel_id} added")
            return True

        except Exception as e:
            logger.error(f"Failed to add forced join channel {channel_id}: {str(e)}")
            bot_logger.log_error(e, context="Add forced join channel")
            return False

    async def remove_forced_join_channel(self, channel_id: str) -> bool:
        """Remove a forced join channel"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "DELETE FROM forced_join_channels WHERE channel_id = ?",
                    (channel_id,)
                )
                await db.commit()

                if cursor.rowcount > 0:
                    logger.info(f"Removed forced join channel: {channel_id}")
                    bot_logger.log_database_operation("REMOVE_FORCED_CHANNEL", f"Channel {channel_id} removed")
                    return True
                else:
                    logger.warning(f"Forced join channel not found: {channel_id}")
                    return False

        except Exception as e:
            logger.error(f"Failed to remove forced join channel {channel_id}: {str(e)}")
            bot_logger.log_error(e, context="Remove forced join channel")
            return False

    async def get_forced_join_channels(self, active_only: bool = True) -> list:
        """Get all forced join channels"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                if active_only:
                    cursor = await db.execute(
                        "SELECT * FROM forced_join_channels WHERE is_active = 1 ORDER BY created_at DESC"
                    )
                else:
                    cursor = await db.execute(
                        "SELECT * FROM forced_join_channels ORDER BY created_at DESC"
                    )

                rows = await cursor.fetchall()
                channels = []

                for row in rows:
                    channels.append({
                        'id': row[0],
                        'channel_id': row[1],
                        'channel_title': row[2],
                        'channel_username': row[3],
                        'is_active': row[4],
                        'added_by': row[5],
                        'created_at': row[6],
                        'updated_at': row[7]
                    })

                return channels

        except Exception as e:
            logger.error(f"Failed to get forced join channels: {str(e)}")
            bot_logger.log_error(e, context="Get forced join channels")
            return []

    async def toggle_forced_join_channel(self, channel_id: str, is_active: bool) -> bool:
        """Toggle forced join channel active status"""
        try:
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute('''
                    UPDATE forced_join_channels
                    SET is_active = ?, updated_at = ?
                    WHERE channel_id = ?
                ''', (1 if is_active else 0, datetime.now().isoformat(), channel_id))
                await db.commit()

                if cursor.rowcount > 0:
                    status = "activated" if is_active else "deactivated"
                    logger.info(f"Forced join channel {channel_id} {status}")
                    bot_logger.log_database_operation("TOGGLE_FORCED_CHANNEL", f"Channel {channel_id} {status}")
                    return True
                else:
                    logger.warning(f"Forced join channel not found: {channel_id}")
                    return False

        except Exception as e:
            logger.error(f"Failed to toggle forced join channel {channel_id}: {str(e)}")
            bot_logger.log_error(e, context="Toggle forced join channel")
            return False

    def close_all_connections(self):
        """Close all database connections"""
        try:
            self.close_excel_connection()
            logger.info("All database connections closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {str(e)}")

# Create global database manager instance
db_manager = DatabaseManager()
